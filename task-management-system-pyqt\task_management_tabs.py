import sys
from PyQt5.QtWidgets import (QWidget, Q<PERSON>oxLayout, QHBoxLayout, QTabWidget, 
                             QTableWidget, QTableWidgetItem, QHeaderView, QPushButton,
                             QLabel, QFrame, QLineEdit, QComboBox, QDateEdit, QTextEdit,
                             QDialog, QFormLayout, QDialogButtonBox, QMessageBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QIcon

class TaskDialog(QDialog):
    """Dialog pour créer/modifier une tâche"""
    
    def __init__(self, task_data=None, parent=None):
        super().__init__(parent)
        self.task_data = task_data
        self.setWindowTitle("Nouvelle Tâche" if not task_data else "Modifier Tâche")
        self.setFixedSize(400, 300)
        self.setup_ui()
        self.apply_style()
        
    def setup_ui(self):
        layout = QFormLayout(self)
        
        # Champs du formulaire
        self.title_edit = QLineEdit()
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["Faible", "Normale", "Élevée", "Urgente"])
        self.status_combo = QComboBox()
        self.status_combo.addItems(["À faire", "En cours", "Terminée"])
        self.due_date = QDateEdit()
        self.due_date.setDate(QDate.currentDate())
        self.due_date.setCalendarPopup(True)
        
        # Remplir les champs si on modifie une tâche
        if self.task_data:
            self.title_edit.setText(self.task_data.get('title', ''))
            self.description_edit.setPlainText(self.task_data.get('description', ''))
            priority_index = ["Faible", "Normale", "Élevée", "Urgente"].index(
                self.task_data.get('priority', 'Normale'))
            self.priority_combo.setCurrentIndex(priority_index)
            status_index = ["À faire", "En cours", "Terminée"].index(
                self.task_data.get('status', 'À faire'))
            self.status_combo.setCurrentIndex(status_index)
        
        layout.addRow("Titre:", self.title_edit)
        layout.addRow("Description:", self.description_edit)
        layout.addRow("Priorité:", self.priority_combo)
        layout.addRow("Statut:", self.status_combo)
        layout.addRow("Date d'échéance:", self.due_date)
        
        # Boutons
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)
        
    def apply_style(self):
        self.setStyleSheet("""
            QDialog {
                background-color: #1A1A2E;
                color: white;
            }
            QLineEdit, QTextEdit, QComboBox, QDateEdit {
                background-color: #2C2C54;
                border: 1px solid #404040;
                border-radius: 5px;
                padding: 8px;
                color: white;
                font-size: 11px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid white;
            }
            QPushButton {
                background-color: #4ECDC4;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5EDDD4;
            }
            QLabel {
                color: #B0B0B0;
                font-weight: bold;
            }
        """)
    
    def get_task_data(self):
        """Retourne les données de la tâche"""
        return {
            'title': self.title_edit.text(),
            'description': self.description_edit.toPlainText(),
            'priority': self.priority_combo.currentText(),
            'status': self.status_combo.currentText(),
            'due_date': self.due_date.date().toString("dd/MM/yyyy")
        }

class TaskTable(QTableWidget):
    """Table moderne pour afficher les tâches"""
    
    task_updated = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()
        self.apply_style()
        
    def setup_table(self):
        # Configuration des colonnes
        headers = ["ID", "Titre", "Description", "Priorité", "Statut", "Date d'échéance", "Actions"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # Configuration de l'en-tête
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Titre
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Description
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Priorité
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Statut
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions
        
        # Configuration générale
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.verticalHeader().setVisible(False)
        
    def apply_style(self):
        self.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A2E;
                alternate-background-color: #2C2C54;
                color: white;
                gridline-color: #404040;
                border: 1px solid #404040;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #4ECDC4;
                color: white;
            }
            QHeaderView::section {
                background-color: #2C2C54;
                color: white;
                padding: 10px;
                border: 1px solid #404040;
                font-weight: bold;
            }
            QScrollBar:vertical {
                background-color: #2C2C54;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #4ECDC4;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #5EDDD4;
            }
        """)
    
    def add_task(self, task_data):
        """Ajoute une tâche à la table"""
        row = self.rowCount()
        self.insertRow(row)
        
        # Données de la tâche
        self.setItem(row, 0, QTableWidgetItem(str(row + 1)))
        self.setItem(row, 1, QTableWidgetItem(task_data['title']))
        self.setItem(row, 2, QTableWidgetItem(task_data['description'][:50] + "..."))
        
        # Priorité avec couleur
        priority_item = QTableWidgetItem(task_data['priority'])
        priority_colors = {
            "Faible": "#96CEB4",
            "Normale": "#45B7D1", 
            "Élevée": "#FECA57",
            "Urgente": "#FF6B6B"
        }
        priority_item.setBackground(QColor(priority_colors.get(task_data['priority'], "#45B7D1")))
        self.setItem(row, 3, priority_item)
        
        # Statut avec couleur
        status_item = QTableWidgetItem(task_data['status'])
        status_colors = {
            "À faire": "#FF6B6B",
            "En cours": "#FECA57",
            "Terminée": "#4ECDC4"
        }
        status_item.setBackground(QColor(status_colors.get(task_data['status'], "#FF6B6B")))
        self.setItem(row, 4, status_item)
        
        self.setItem(row, 5, QTableWidgetItem(task_data['due_date']))
        
        # Boutons d'action
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 5, 5, 5)
        
        edit_btn = QPushButton("✏️")
        edit_btn.setFixedSize(30, 30)
        edit_btn.clicked.connect(lambda: self.edit_task(row))
        
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(30, 30)
        delete_btn.clicked.connect(lambda: self.delete_task(row))
        
        actions_layout.addWidget(edit_btn)
        actions_layout.addWidget(delete_btn)
        
        self.setCellWidget(row, 6, actions_widget)
    
    def edit_task(self, row):
        """Édite une tâche"""
        task_data = {
            'title': self.item(row, 1).text(),
            'description': self.item(row, 2).text().replace("...", ""),
            'priority': self.item(row, 3).text(),
            'status': self.item(row, 4).text(),
            'due_date': self.item(row, 5).text()
        }
        
        dialog = TaskDialog(task_data, self)
        if dialog.exec_() == QDialog.Accepted:
            new_data = dialog.get_task_data()
            self.update_task_row(row, new_data)
            self.task_updated.emit(new_data)
    
    def delete_task(self, row):
        """Supprime une tâche"""
        reply = QMessageBox.question(self, "Confirmer", 
                                   "Êtes-vous sûr de vouloir supprimer cette tâche ?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.removeRow(row)
    
    def update_task_row(self, row, task_data):
        """Met à jour une ligne de tâche"""
        self.setItem(row, 1, QTableWidgetItem(task_data['title']))
        self.setItem(row, 2, QTableWidgetItem(task_data['description'][:50] + "..."))
        
        # Mise à jour avec couleurs
        priority_item = QTableWidgetItem(task_data['priority'])
        priority_colors = {
            "Faible": "#96CEB4",
            "Normale": "#45B7D1", 
            "Élevée": "#FECA57",
            "Urgente": "#FF6B6B"
        }
        priority_item.setBackground(QColor(priority_colors.get(task_data['priority'], "#45B7D1")))
        self.setItem(row, 3, priority_item)
        
        status_item = QTableWidgetItem(task_data['status'])
        status_colors = {
            "À faire": "#FF6B6B",
            "En cours": "#FECA57",
            "Terminée": "#4ECDC4"
        }
        status_item.setBackground(QColor(status_colors.get(task_data['status'], "#FF6B6B")))
        self.setItem(row, 4, status_item)
        
        self.setItem(row, 5, QTableWidgetItem(task_data['due_date']))

class TaskManagementTabs(QWidget):
    """Widget principal avec onglets pour la gestion des tâches"""
    
    def __init__(self, bureau_name="Bureau", parent=None):
        super().__init__(parent)
        self.bureau_name = bureau_name
        self.setup_ui()
        self.apply_style()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # En-tête compact
        header_layout = QHBoxLayout()

        # Bouton pour ajouter une tâche (plus compact)
        add_btn = QPushButton("➕ Nouvelle Tâche")
        add_btn.setFixedHeight(35)
        add_btn.setFixedWidth(150)
        add_btn.clicked.connect(self.add_new_task)

        header_layout.addWidget(add_btn)
        header_layout.addStretch()
        
        # Onglets pour les différents types de tâches
        self.tab_widget = QTabWidget()
        self.tab_widget.setMaximumHeight(280)  # Limiter la hauteur des onglets

        # Onglet "À faire"
        self.todo_table = TaskTable()
        self.todo_table.setMaximumHeight(240)  # Limiter la hauteur de la table
        self.tab_widget.addTab(self.todo_table, "📋 À Faire")

        # Onglet "En cours"
        self.inprogress_table = TaskTable()
        self.inprogress_table.setMaximumHeight(240)
        self.tab_widget.addTab(self.inprogress_table, "⚡ En Cours")

        # Onglet "Terminées"
        self.completed_table = TaskTable()
        self.completed_table.setMaximumHeight(240)
        self.tab_widget.addTab(self.completed_table, "✅ Terminées")
        
        layout.addLayout(header_layout)
        layout.addWidget(self.tab_widget)
        
        # Connecter les signaux
        self.todo_table.task_updated.connect(self.handle_task_update)
        self.inprogress_table.task_updated.connect(self.handle_task_update)
        self.completed_table.task_updated.connect(self.handle_task_update)
        
    def apply_style(self):
        self.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #404040;
                border-radius: 8px;
                background-color: #1A1A2E;
            }
            QTabBar::tab {
                background-color: #2C2C54;
                color: white;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #4ECDC4;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5EDDD4;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4ECDC4, stop:1 #3BB5AE);
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #5EDDD4, stop:1 #4ECDC4);
            }
        """)
    
    def add_new_task(self):
        """Ajoute une nouvelle tâche"""
        dialog = TaskDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            task_data = dialog.get_task_data()
            
            # Ajouter à la table appropriée selon le statut
            if task_data['status'] == "À faire":
                self.todo_table.add_task(task_data)
            elif task_data['status'] == "En cours":
                self.inprogress_table.add_task(task_data)
            else:  # Terminée
                self.completed_table.add_task(task_data)
    
    def handle_task_update(self, task_data):
        """Gère la mise à jour d'une tâche (changement de statut)"""
        # Cette méthode pourrait être étendue pour déplacer les tâches
        # entre les onglets selon leur nouveau statut
        pass

if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication, QMainWindow
    
    app = QApplication(sys.argv)
    
    window = QMainWindow()
    window.setWindowTitle("Gestion des Tâches - Test")
    window.setGeometry(100, 100, 1200, 700)
    
    # Style sombre
    window.setStyleSheet("""
        QMainWindow {
            background-color: #0F0F23;
        }
    """)
    
    task_widget = TaskManagementTabs("Bureau Test")
    window.setCentralWidget(task_widget)
    
    window.show()
    sys.exit(app.exec_())
