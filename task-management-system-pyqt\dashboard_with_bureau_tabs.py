import sys
from PyQt5.QtWidgets import (<PERSON>Application, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QFrame, QStackedWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import <PERSON><PERSON>ont, QPalette, QColor

# Import des composants
from chart_widgets import MetricCard
from bureau_task_manager import BureauTaskManager

class ModernButton(QPushButton):
    """Bouton moderne avec couleurs personnalisées"""
    
    def __init__(self, text, color, parent=None):
        super().__init__(text, parent)
        self.color = color
        self.setFixedHeight(50)
        self.setFont(QFont("Segoe UI", 10, QFont.Bold))
        self.setup_style()
        
    def setup_style(self):
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}, stop:1 {self.darken_color(self.color)});
                border: none;
                border-radius: 12px;
                color: white;
                text-align: left;
                padding-left: 20px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.lighten_color(self.color)}, stop:1 {self.color});
            }}
            QPushButton:pressed {{
                background: {self.darken_color(self.color)};
            }}
        """)
    
    def darken_color(self, color):
        """Assombrit une couleur"""
        color_map = {
            "#FF6B6B": "#E55555", "#4ECDC4": "#3BB5AE", "#45B7D1": "#3A9BC1",
            "#96CEB4": "#7FB89A", "#FECA57": "#E6B547", "#FF9FF3": "#E68FD3",
            "#54A0FF": "#4490EF", "#5F27CD": "#4F1FAD"
        }
        return color_map.get(color, color)
    
    def lighten_color(self, color):
        """Éclaircit une couleur"""
        color_map = {
            "#FF6B6B": "#FF7B7B", "#4ECDC4": "#5EDDD4", "#45B7D1": "#55C7E1",
            "#96CEB4": "#A6DEC4", "#FECA57": "#FEDA67", "#FF9FF3": "#FFAFF3",
            "#54A0FF": "#64B0FF", "#5F27CD": "#6F37DD"
        }
        return color_map.get(color, color)

class DashboardPage(QWidget):
    """Page de dashboard pour chaque bureau"""
    
    def __init__(self, bureau_name, task_manager, parent=None):
        super().__init__(parent)
        self.bureau_name = bureau_name
        self.task_manager = task_manager
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête
        header = QLabel(f"Dashboard - {self.bureau_name}")
        header.setFont(QFont("Segoe UI", 18, QFont.Bold))
        header.setStyleSheet("color: white; margin-bottom: 20px;")
        
        # Cartes de statistiques
        stats_layout = QHBoxLayout()
        
        completed_card = MetricCard("Tâches Complétées", "24", "↑ 12% ce mois", "progress")
        in_progress_card = MetricCard("En Cours", "8", "↓ 3 depuis hier", "progress")
        upcoming_card = MetricCard("Performance", "89%", "↑ 5% cette semaine", "line")
        
        stats_layout.addWidget(completed_card)
        stats_layout.addWidget(in_progress_card)
        stats_layout.addWidget(upcoming_card)
        stats_layout.addStretch()
        
        # Section de gestion des tâches
        task_section = QFrame()
        task_section.setStyleSheet("""
            QFrame {
                background-color: #2C2C54;
                border-radius: 15px;
                margin-top: 20px;
            }
        """)
        task_section.setMinimumHeight(500)
        
        task_layout = QVBoxLayout(task_section)
        task_layout.setContentsMargins(10, 10, 10, 10)
        
        # Titre de la section tâches
        task_title = QLabel("Gestion des Tâches par Bureau")
        task_title.setFont(QFont("Segoe UI", 14, QFont.Bold))
        task_title.setStyleSheet("color: white; margin-bottom: 10px;")
        
        task_layout.addWidget(task_title)
        task_layout.addWidget(self.task_manager)
        
        layout.addWidget(header)
        layout.addLayout(stats_layout)
        layout.addWidget(task_section)

class DashboardWithBureauTabs(QMainWindow):
    """Dashboard principal avec système de tâches par bureau"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Dashboard Moderne - Gestion par Bureau")
        self.setGeometry(100, 100, 1400, 800)
        
        # Créer un gestionnaire de tâches global
        self.task_manager = BureauTaskManager()
        
        self.setup_ui()
        self.apply_dark_theme()
        
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Sidebar
        self.create_sidebar(main_layout)
        
        # Zone de contenu principal
        self.content_stack = QStackedWidget()
        self.create_content_pages()
        
        main_layout.addWidget(self.content_stack, 1)
        
    def create_sidebar(self, main_layout):
        """Crée la sidebar avec les boutons colorés"""
        sidebar = QFrame()
        sidebar.setFixedWidth(250)
        sidebar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1A1A2E, stop:1 #16213E);
                border-right: 1px solid #404040;
            }
        """)
        
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(15, 20, 15, 20)
        sidebar_layout.setSpacing(10)
        
        # Logo/Titre
        title = QLabel("TASK MANAGER")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title.setStyleSheet("color: white; margin-bottom: 30px; text-align: center;")
        title.setAlignment(Qt.AlignCenter)
        
        sidebar_layout.addWidget(title)
        
        # Boutons de navigation
        self.nav_buttons = []
        bureaux = [
            ("🏢 1B", "#FF6B6B"),
            ("🏢 2B", "#4ECDC4"), 
            ("🏢 3B", "#45B7D1"),
            ("🏢 4B", "#96CEB4"),
            ("🏢 5B", "#FECA57"),
            ("🧠 B.GENIE", "#FF9FF3"),
            ("🚚 C.TRANS", "#54A0FF"),
            ("📋 SECRETARIAT", "#5F27CD")
        ]
        
        for i, (name, color) in enumerate(bureaux):
            btn = ModernButton(name, color)
            btn.clicked.connect(lambda checked, idx=i: self.switch_page(idx))
            self.nav_buttons.append(btn)
            sidebar_layout.addWidget(btn)
        
        sidebar_layout.addStretch()
        
        # Bouton de déconnexion
        logout_btn = ModernButton("🚪 Déconnexion", "#E74C3C")
        sidebar_layout.addWidget(logout_btn)
        
        main_layout.addWidget(sidebar)
        
    def create_content_pages(self):
        """Crée les pages de contenu pour chaque bureau"""
        bureaux_names = ["1B", "2B", "3B", "4B", "5B", "B.GENIE", "C.TRANS", "SECRETARIAT"]
        
        for bureau in bureaux_names:
            page = DashboardPage(bureau, self.task_manager)
            self.content_stack.addWidget(page)
    
    def switch_page(self, index):
        """Change la page affichée et bascule vers l'onglet du bureau correspondant"""
        self.content_stack.setCurrentIndex(index)
        
        # Basculer vers l'onglet du bureau correspondant dans le gestionnaire de tâches
        bureaux_names = ["1B", "2B", "3B", "4B", "5B", "B.GENIE", "C.TRANS", "SECRETARIAT"]
        if 0 <= index < len(bureaux_names):
            bureau_name = bureaux_names[index]
            self.task_manager.switch_to_bureau(bureau_name)
        
        # Animation de sélection du bouton
        for i, btn in enumerate(self.nav_buttons):
            if i == index:
                btn.setStyleSheet(btn.styleSheet() + """
                    QPushButton {
                        border-left: 4px solid #00D4FF;
                    }
                """)
            else:
                # Réinitialise le style
                btn.setup_style()
    
    def apply_dark_theme(self):
        """Applique le thème sombre moderne"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #0F0F23;
                color: white;
            }
            QWidget {
                background-color: transparent;
            }
        """)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setStyle("Fusion")
    
    # Palette sombre
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(15, 15, 35))
    palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
    app.setPalette(palette)
    
    dashboard = DashboardWithBureauTabs()
    dashboard.show()
    
    sys.exit(app.exec_())
