<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RegistrationSuccessDialog</class>
 <widget class="QDialog" name="RegistrationSuccessDialog">
  <property name="windowModality">
   <enum>Qt::ApplicationModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>435</width>
    <height>314</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>435</width>
    <height>314</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>435</width>
    <height>314</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Registration Successfull</string>
  </property>
  <property name="windowOpacity">
   <double>0.930000000000000</double>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(0, 0, 0);</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QFrame" name="top_frame">
     <property name="styleSheet">
      <string notr="true">background-color: rgb(46, 52, 54);
border-radius:20px;</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <item alignment="Qt::AlignHCenter">
       <widget class="QLabel" name="label">
        <property name="font">
         <font>
          <pointsize>18</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(52, 101, 164);
border-color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>Task Management System</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item alignment="Qt::AlignHCenter">
    <widget class="QFrame" name="mid_frame">
     <property name="styleSheet">
      <string notr="true">background-color: rgb(46, 52, 54);
border-radius:20px;</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <widget class="QLabel" name="reg_label">
        <property name="font">
         <font>
          <pointsize>18</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>Registration Successfull</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item alignment="Qt::AlignHCenter|Qt::AlignBottom">
    <widget class="QFrame" name="bottom_frame">
     <property name="styleSheet">
      <string notr="true">QFrame#bottom_frame{
	background-color: rgb(46, 52, 54);
	border-radius:10px;
}

</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item alignment="Qt::AlignHCenter">
       <widget class="QPushButton" name="ok_button">
        <property name="focusPolicy">
         <enum>Qt::WheelFocus</enum>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="autoFillBackground">
         <bool>false</bool>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton#ok_button{
	background-color: rgb(255, 255, 255);
}</string>
        </property>
        <property name="text">
         <string>OK</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
