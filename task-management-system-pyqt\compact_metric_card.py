import sys
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont, QLinearGradient, QConicalGradient

class CompactCircularProgress(QWidget):
    """Barre de progression circulaire compacte"""
    
    def __init__(self, value=0, max_value=100, color="#4ECDC4", parent=None):
        super().__init__(parent)
        self.value = value
        self.max_value = max_value
        self.color = QColor(color)
        self.setFixedSize(60, 60)  # Taille compacte
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Dimensions
        rect = self.rect()
        center = rect.center()
        radius = min(rect.width(), rect.height()) // 2 - 5
        
        # Cercle de fond
        painter.setPen(QPen(QColor("#2C2C54"), 4))
        painter.drawEllipse(center.x() - radius, center.y() - radius, 
                          radius * 2, radius * 2)
        
        # Cercle de progression
        progress_angle = int((self.value / self.max_value) * 360 * 16)
        
        painter.setPen(QPen(self.color, 4, Qt.SolidLine, Qt.RoundCap))
        painter.drawArc(center.x() - radius, center.y() - radius,
                       radius * 2, radius * 2, 90 * 16, -progress_angle)

class CompactLineChart(QWidget):
    """Graphique linéaire compact"""
    
    def __init__(self, data=None, color="#45B7D1", parent=None):
        super().__init__(parent)
        self.data = data or [20, 35, 30, 45, 40, 55]
        self.color = QColor(color)
        self.setFixedSize(60, 30)  # Très compact
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        rect = self.rect()
        margin = 2
        draw_rect = rect.adjusted(margin, margin, -margin, -margin)
        
        if len(self.data) < 2:
            return
            
        # Calcul des points
        max_val = max(self.data)
        min_val = min(self.data)
        val_range = max_val - min_val if max_val != min_val else 1
        
        points = []
        for i, val in enumerate(self.data):
            x = draw_rect.left() + (i / (len(self.data) - 1)) * draw_rect.width()
            y = draw_rect.bottom() - ((val - min_val) / val_range) * draw_rect.height()
            points.append((x, y))
        
        # Dessiner la ligne
        painter.setPen(QPen(self.color, 2))
        for i in range(len(points) - 1):
            painter.drawLine(int(points[i][0]), int(points[i][1]),
                           int(points[i+1][0]), int(points[i+1][1]))

class CompactMetricCard(QFrame):
    """Carte de métrique compacte avec taille flexible"""
    
    def __init__(self, title, value, change, chart_type="progress", parent=None):
        super().__init__(parent)
        # Taille flexible - peut être modifiée après création
        self.setMinimumSize(180, 100)
        self.setMaximumSize(350, 200)
        self.resize(230, 130)  # Taille par défaut compacte
        self.setup_ui(title, value, change, chart_type)
        
    def setup_ui(self, title, value, change, chart_type):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 10, 12, 10)  # Marges réduites
        
        # Partie gauche - Texte
        text_layout = QVBoxLayout()
        text_layout.setSpacing(2)  # Espacement réduit
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 9))  # Police plus petite
        title_label.setStyleSheet("color: #B0B0B0;")
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 20, QFont.Bold))  # Police réduite
        value_label.setStyleSheet("color: white;")
        
        change_label = QLabel(change)
        change_label.setFont(QFont("Segoe UI", 8))  # Police plus petite
        change_color = "#4ECDC4" if "↑" in change else "#FF6B6B"
        change_label.setStyleSheet(f"color: {change_color};")
        
        text_layout.addWidget(title_label)
        text_layout.addWidget(value_label)
        text_layout.addWidget(change_label)
        text_layout.addStretch()
        
        # Partie droite - Graphique compact
        if chart_type == "progress":
            chart = CompactCircularProgress(75, 100, "#4ECDC4")
        elif chart_type == "line":
            chart = CompactLineChart([20, 35, 30, 45, 40, 55], "#45B7D1")
        else:
            chart = CompactCircularProgress(60, 100, "#FF6B6B")
        
        layout.addLayout(text_layout)
        layout.addWidget(chart)
        
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2C2C54, stop:1 #1A1A2E);
                border-radius: 12px;
                border: 1px solid #404040;
            }
        """)

class FlexibleMetricCard(QFrame):
    """Carte de métrique avec taille entièrement flexible"""
    
    def __init__(self, title, value, change, chart_type="progress", parent=None):
        super().__init__(parent)
        # Aucune contrainte de taille - entièrement flexible
        self.setup_ui(title, value, change, chart_type)
        
    def setup_ui(self, title, value, change, chart_type):
        layout = QVBoxLayout(self)  # Layout vertical pour plus de compacité
        layout.setContentsMargins(10, 8, 10, 8)
        layout.setSpacing(3)
        
        # Titre
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 9, QFont.Bold))
        title_label.setStyleSheet("color: #B0B0B0;")
        title_label.setAlignment(Qt.AlignCenter)
        
        # Valeur principale
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        value_label.setStyleSheet("color: white;")
        value_label.setAlignment(Qt.AlignCenter)
        
        # Changement
        change_label = QLabel(change)
        change_label.setFont(QFont("Segoe UI", 8))
        change_color = "#4ECDC4" if "↑" in change else "#FF6B6B"
        change_label.setStyleSheet(f"color: {change_color};")
        change_label.setAlignment(Qt.AlignCenter)
        
        # Graphique compact en bas
        if chart_type == "progress":
            chart = CompactCircularProgress(75, 100, "#4ECDC4")
        elif chart_type == "line":
            chart = CompactLineChart([20, 35, 30, 45, 40, 55], "#45B7D1")
        else:
            chart = CompactCircularProgress(60, 100, "#FF6B6B")
        
        chart_layout = QHBoxLayout()
        chart_layout.addStretch()
        chart_layout.addWidget(chart)
        chart_layout.addStretch()
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addWidget(change_label)
        layout.addLayout(chart_layout)
        
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2C2C54, stop:1 #1A1A2E);
                border-radius: 10px;
                border: 1px solid #404040;
            }
        """)

if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication, QMainWindow, QHBoxLayout, QWidget
    
    app = QApplication(sys.argv)
    
    window = QMainWindow()
    window.setWindowTitle("Cartes Métriques Compactes")
    window.setGeometry(100, 100, 800, 300)
    
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    
    layout = QHBoxLayout(central_widget)
    
    # Test des différentes cartes
    compact_card = CompactMetricCard("Complétées", "24", "↑ 12%", "progress")
    compact_card.setFixedSize(220, 120)  # Test de redimensionnement
    
    flexible_card = FlexibleMetricCard("En Cours", "8", "↓ 3", "progress")
    flexible_card.setFixedSize(200, 140)  # Test de redimensionnement
    
    line_card = CompactMetricCard("Performance", "92%", "↑ 5%", "line")
    line_card.setFixedSize(240, 110)  # Test de redimensionnement
    
    layout.addWidget(compact_card)
    layout.addWidget(flexible_card)
    layout.addWidget(line_card)
    
    # Style sombre
    window.setStyleSheet("""
        QMainWindow {
            background-color: #0F0F23;
        }
    """)
    
    window.show()
    sys.exit(app.exec_())
