import sys
from PyQt5.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QFrame, QStackedWidget,
                             QScrollArea, QGridLayout, QProgressBar, QTextEdit, QTabWidget)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor, QPixmap, QPainter, QBrush, QLinearGradient

# Import des composants de graphiques et de gestion des tâches
from chart_widgets import CircularProgressBar, LineChart, DonutChart, BarChart, MetricCard
from compact_task_widget import CompactTaskWidget
from bureau_task_manager import BureauTaskManager

class ModernButton(QPushButton):
    """Bouton moderne avec animations et couleurs personnalisées"""
    
    def __init__(self, text, color, parent=None):
        super().__init__(text, parent)
        self.color = color
        self.setFixedHeight(50)
        self.setFont(QFont("Segoe UI", 10, QFont.Bold))
        self.setup_style()
        
    def setup_style(self):
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}, stop:1 {self.darken_color(self.color)});
                border: none;
                border-radius: 12px;
                color: white;
                text-align: left;
                padding-left: 20px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.lighten_color(self.color)}, stop:1 {self.color});
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background: {self.darken_color(self.color)};
            }}
        """)
    
    def darken_color(self, color):
        """Assombrit une couleur"""
        if color == "#FF6B6B": return "#E55555"
        elif color == "#4ECDC4": return "#3BB5AE"
        elif color == "#45B7D1": return "#3A9BC1"
        elif color == "#96CEB4": return "#7FB89A"
        elif color == "#FECA57": return "#E6B547"
        elif color == "#FF9FF3": return "#E68FD3"
        elif color == "#54A0FF": return "#4490EF"
        elif color == "#5F27CD": return "#4F1FAD"
        return color
    
    def lighten_color(self, color):
        """Éclaircit une couleur"""
        if color == "#FF6B6B": return "#FF7B7B"
        elif color == "#4ECDC4": return "#5EDDD4"
        elif color == "#45B7D1": return "#55C7E1"
        elif color == "#96CEB4": return "#A6DEC4"
        elif color == "#FECA57": return "#FEDA67"
        elif color == "#FF9FF3": return "#FFAFF3"
        elif color == "#54A0FF": return "#64B0FF"
        elif color == "#5F27CD": return "#6F37DD"
        return color

class StatCard(QFrame):
    """Carte de statistiques moderne"""
    
    def __init__(self, title, value, subtitle, color, parent=None):
        super().__init__(parent)
        self.setFixedSize(200, 120)
        self.setup_ui(title, value, subtitle, color)
        
    def setup_ui(self, title, value, subtitle, color):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Titre
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 9))
        title_label.setStyleSheet("color: #B0B0B0;")
        
        # Valeur principale
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 24, QFont.Bold))
        value_label.setStyleSheet("color: white;")
        
        # Sous-titre
        subtitle_label = QLabel(subtitle)
        subtitle_label.setFont(QFont("Segoe UI", 8))
        subtitle_label.setStyleSheet("color: #808080;")
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addWidget(subtitle_label)
        layout.addStretch()
        
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:1 {self.darken_color(color)});
                border-radius: 15px;
                margin: 10px;
            }}
        """)
    
    def darken_color(self, color):
        """Assombrit une couleur pour le gradient"""
        color_map = {
            "#FF6B6B": "#E55555",
            "#4ECDC4": "#3BB5AE", 
            "#45B7D1": "#3A9BC1",
            "#96CEB4": "#7FB89A"
        }
        return color_map.get(color, color)

class TaskPage(QWidget):
    """Page de gestion des tâches pour chaque bureau"""
    
    def __init__(self, bureau_name, parent=None):
        super().__init__(parent)
        self.bureau_name = bureau_name
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête
        header = QLabel(f"Gestion des Tâches - {self.bureau_name}")
        header.setFont(QFont("Segoe UI", 18, QFont.Bold))
        header.setStyleSheet("color: white; margin-bottom: 20px;")
        
        # Cartes de statistiques modernes avec graphiques
        stats_layout = QHBoxLayout()

        # Cartes métriques avec graphiques intégrés
        completed_card = MetricCard("Tâches Complétées", "24", "↑ 12% ce mois", "progress")
        in_progress_card = MetricCard("En Cours", "8", "↓ 3 depuis hier", "progress")
        upcoming_card = MetricCard("Performance", "89%", "↑ 5% cette semaine", "line")

        stats_layout.addWidget(completed_card)
        stats_layout.addWidget(in_progress_card)
        stats_layout.addWidget(upcoming_card)
        stats_layout.addStretch()
        
        # Zone de contenu principal avec graphiques
        content_area = QFrame()
        content_area.setStyleSheet("""
            QFrame {
                background-color: #2C2C54;
                border-radius: 15px;
                margin-top: 20px;
            }
        """)
        content_area.setMinimumHeight(500)

        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(20, 20, 20, 20)

        # Titre de la section
        section_title = QLabel(f"Tableau de Bord - {self.bureau_name}")
        section_title.setFont(QFont("Segoe UI", 14, QFont.Bold))
        section_title.setStyleSheet("color: white; margin-bottom: 15px;")

        # Layout pour les graphiques
        charts_layout = QHBoxLayout()

        # Graphique linéaire - Évolution des tâches
        line_frame = QFrame()
        line_frame.setStyleSheet("""
            QFrame {
                background-color: #1A1A2E;
                border-radius: 10px;
                border: 1px solid #404040;
            }
        """)
        line_layout = QVBoxLayout(line_frame)
        line_title = QLabel("Évolution des Tâches")
        line_title.setFont(QFont("Segoe UI", 11, QFont.Bold))
        line_title.setStyleSheet("color: white; margin: 10px;")
        line_chart = LineChart([15, 25, 20, 35, 30, 45, 40], "#45B7D1")
        line_layout.addWidget(line_title)
        line_layout.addWidget(line_chart)

        # Graphique donut - Répartition par type
        donut_frame = QFrame()
        donut_frame.setStyleSheet("""
            QFrame {
                background-color: #1A1A2E;
                border-radius: 10px;
                border: 1px solid #404040;
            }
        """)
        donut_layout = QVBoxLayout(donut_frame)
        donut_title = QLabel("Répartition des Tâches")
        donut_title.setFont(QFont("Segoe UI", 11, QFont.Bold))
        donut_title.setStyleSheet("color: white; margin: 10px;")
        donut_chart = DonutChart([30, 25, 20, 15, 10],
                                ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FECA57"])
        donut_layout.addWidget(donut_title)
        donut_layout.addWidget(donut_chart, 0, Qt.AlignCenter)

        charts_layout.addWidget(line_frame)
        charts_layout.addWidget(donut_frame)

        # Graphique en barres - Performance hebdomadaire
        bar_frame = QFrame()
        bar_frame.setStyleSheet("""
            QFrame {
                background-color: #1A1A2E;
                border-radius: 10px;
                border: 1px solid #404040;
                margin-top: 15px;
            }
        """)
        bar_layout = QVBoxLayout(bar_frame)
        bar_title = QLabel("Performance Hebdomadaire")
        bar_title.setFont(QFont("Segoe UI", 11, QFont.Bold))
        bar_title.setStyleSheet("color: white; margin: 10px;")
        bar_chart = BarChart([45, 60, 35, 80, 55, 70, 40],
                            ["Lun", "Mar", "Mer", "Jeu", "Ven", "Sam", "Dim"], "#54A0FF")
        bar_layout.addWidget(bar_title)
        bar_layout.addWidget(bar_chart)

        content_layout.addWidget(section_title)
        content_layout.addLayout(charts_layout)
        content_layout.addWidget(bar_frame)

        # Section de gestion des tâches - Version simplifiée
        task_section = QFrame()
        task_section.setStyleSheet("""
            QFrame {
                background-color: #2C2C54;
                border-radius: 15px;
                margin-top: 15px;
            }
        """)
        task_section.setMinimumHeight(400)

        task_layout = QVBoxLayout(task_section)
        task_layout.setContentsMargins(15, 15, 15, 15)

        # Titre de la section tâches
        task_title = QLabel("Gestion des Tâches")
        task_title.setFont(QFont("Segoe UI", 14, QFont.Bold))
        task_title.setStyleSheet("color: white; margin-bottom: 10px;")

        # Utiliser le gestionnaire de tâches global du dashboard
        # (sera défini lors de la création de la page)

        task_layout.addWidget(task_title)
        task_layout.addWidget(task_manager)

        layout.addWidget(header)
        layout.addLayout(stats_layout)
        layout.addWidget(content_area)
        layout.addWidget(task_section)

class ModernDashboard(QMainWindow):
    """Dashboard principal moderne"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Système de Gestion des Tâches - Dashboard Moderne")
        self.setGeometry(100, 100, 1400, 800)

        # Créer un gestionnaire de tâches global partagé
        self.global_task_manager = BureauTaskManager()

        self.setup_ui()
        self.apply_dark_theme()

    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Sidebar
        self.create_sidebar(main_layout)

        # Zone de contenu principal
        self.content_stack = QStackedWidget()
        self.create_content_pages()

        main_layout.addWidget(self.content_stack, 1)
        
    def create_sidebar(self, main_layout):
        """Crée la sidebar avec les boutons colorés"""
        sidebar = QFrame()
        sidebar.setFixedWidth(250)
        sidebar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1A1A2E, stop:1 #16213E);
                border-right: 1px solid #404040;
            }
        """)
        
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(15, 20, 15, 20)
        sidebar_layout.setSpacing(10)
        
        # Logo/Titre
        title = QLabel("TASK MANAGER")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title.setStyleSheet("color: white; margin-bottom: 30px; text-align: center;")
        title.setAlignment(Qt.AlignCenter)
        
        sidebar_layout.addWidget(title)
        
        # Boutons de navigation
        self.nav_buttons = []
        bureaux = [
            ("🏢 1B", "#FF6B6B"),
            ("🏢 2B", "#4ECDC4"), 
            ("🏢 3B", "#45B7D1"),
            ("🏢 4B", "#96CEB4"),
            ("🏢 5B", "#FECA57"),
            ("🧠 B.GENIE", "#FF9FF3"),
            ("🚚 C.TRANS", "#54A0FF"),
            ("📋 SECRETARIAT", "#5F27CD")
        ]
        
        for i, (name, color) in enumerate(bureaux):
            btn = ModernButton(name, color)
            btn.clicked.connect(lambda checked, idx=i: self.switch_page(idx))
            self.nav_buttons.append(btn)
            sidebar_layout.addWidget(btn)
        
        sidebar_layout.addStretch()
        
        # Bouton de déconnexion
        logout_btn = ModernButton("🚪 Déconnexion", "#E74C3C")
        sidebar_layout.addWidget(logout_btn)
        
        main_layout.addWidget(sidebar)
        
    def create_content_pages(self):
        """Crée les pages de contenu pour chaque bureau"""
        bureaux_names = ["1B", "2B", "3B", "4B", "5B", "B.GENIE", "C.TRANS", "SECRETARIAT"]
        
        for bureau in bureaux_names:
            page = TaskPage(bureau)
            self.content_stack.addWidget(page)
    
    def switch_page(self, index):
        """Change la page affichée et bascule vers l'onglet du bureau correspondant"""
        self.content_stack.setCurrentIndex(index)

        # Basculer vers l'onglet du bureau correspondant dans le gestionnaire de tâches
        bureaux_names = ["1B", "2B", "3B", "4B", "5B", "B.GENIE", "C.TRANS", "SECRETARIAT"]
        if 0 <= index < len(bureaux_names):
            bureau_name = bureaux_names[index]
            self.global_task_manager.switch_to_bureau(bureau_name)

        # Animation de sélection du bouton
        for i, btn in enumerate(self.nav_buttons):
            if i == index:
                btn.setStyleSheet(btn.styleSheet() + """
                    QPushButton {
                        border-left: 4px solid #00D4FF;
                    }
                """)
            else:
                # Réinitialise le style
                btn.setup_style()
    
    def apply_dark_theme(self):
        """Applique le thème sombre moderne"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #0F0F23;
                color: white;
            }
            QWidget {
                background-color: transparent;
            }
        """)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setStyle("Fusion")
    
    # Palette sombre
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(15, 15, 35))
    palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
    app.setPalette(palette)
    
    dashboard = ModernDashboard()
    dashboard.show()
    
    sys.exit(app.exec_())
