<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CreateTask</class>
 <widget class="QDialog" name="CreateTask">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>676</width>
    <height>806</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>676</width>
    <height>806</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>676</width>
    <height>806</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>CREATE TASK</string>
  </property>
  <property name="windowOpacity">
   <double>0.980000000000000</double>
  </property>
  <property name="sizeGripEnabled">
   <bool>true</bool>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QFrame" name="top_frame">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>150</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(136, 138, 133);</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QFrame" name="frame_3">
        <property name="styleSheet">
         <string notr="true">background-color: rgb(46, 52, 54);</string>
        </property>
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item alignment="Qt::AlignHCenter">
          <widget class="QLabel" name="label">
           <property name="font">
            <font>
             <family>Ubuntu Condensed</family>
             <pointsize>23</pointsize>
             <weight>75</weight>
             <italic>true</italic>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color: rgb(255, 255, 255);</string>
           </property>
           <property name="text">
            <string>CREATE TASK</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="frame_2">
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QFrame" name="frame_10">
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_8">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>9</number>
         </property>
         <item>
          <widget class="QFrame" name="left_frame">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(46, 52, 54);</string>
           </property>
           <property name="frameShape">
            <enum>QFrame::NoFrame</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <item>
             <widget class="QFrame" name="frame_5">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>35</height>
               </size>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Raised</enum>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_3">
               <property name="topMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QLabel" name="label_2">
                 <property name="font">
                  <font>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);</string>
                 </property>
                 <property name="text">
                  <string>TASK TITLE</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QFrame" name="frame_6">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>300</height>
               </size>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Raised</enum>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_4">
               <item>
                <widget class="QLabel" name="label_3">
                 <property name="font">
                  <font>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);</string>
                 </property>
                 <property name="text">
                  <string>DESCRIPTION</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QFrame" name="frame_7">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>50</height>
               </size>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Raised</enum>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_5">
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QLabel" name="label_4">
                 <property name="font">
                  <font>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);</string>
                 </property>
                 <property name="text">
                  <string>CREATED AT</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item alignment="Qt::AlignTop">
             <widget class="QFrame" name="frame_8">
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Raised</enum>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_6">
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item alignment="Qt::AlignTop">
                <widget class="QLabel" name="label_5">
                 <property name="font">
                  <font>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);</string>
                 </property>
                 <property name="text">
                  <string>UPDATED AT</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="mid_frame">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(238, 238, 236);</string>
           </property>
           <property name="frameShape">
            <enum>QFrame::NoFrame</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_5">
            <property name="rightMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QFrame" name="frame_12">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>45</height>
               </size>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Raised</enum>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_7">
               <item>
                <widget class="QLineEdit" name="title_input"/>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QFrame" name="frame_13">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>300</height>
               </size>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Raised</enum>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_12">
               <property name="topMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QTextEdit" name="description_input">
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>300</height>
                  </size>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QFrame" name="frame_14">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Raised</enum>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_10">
               <item alignment="Qt::AlignLeft">
                <widget class="QDateEdit" name="created_input">
                 <property name="font">
                  <font>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="maximumDateTime">
                  <datetime>
                   <hour>23</hour>
                   <minute>59</minute>
                   <second>59</second>
                   <year>3000</year>
                   <month>12</month>
                   <day>31</day>
                  </datetime>
                 </property>
                 <property name="minimumDateTime">
                  <datetime>
                   <hour>0</hour>
                   <minute>0</minute>
                   <second>0</second>
                   <year>1900</year>
                   <month>1</month>
                   <day>1</day>
                  </datetime>
                 </property>
                 <property name="displayFormat">
                  <string>dd/MM/yyyy</string>
                 </property>
                 <property name="calendarPopup">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QFrame" name="frame_15">
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Raised</enum>
              </property>
              <layout class="QFormLayout" name="formLayout">
               <property name="topMargin">
                <number>8</number>
               </property>
               <item row="0" column="0">
                <widget class="QDateEdit" name="updated_input">
                 <property name="font">
                  <font>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="maximumDateTime">
                  <datetime>
                   <hour>23</hour>
                   <minute>59</minute>
                   <second>59</second>
                   <year>3000</year>
                   <month>12</month>
                   <day>31</day>
                  </datetime>
                 </property>
                 <property name="minimumDateTime">
                  <datetime>
                   <hour>0</hour>
                   <minute>0</minute>
                   <second>0</second>
                   <year>1900</year>
                   <month>1</month>
                   <day>1</day>
                  </datetime>
                 </property>
                 <property name="displayFormat">
                  <string>dd/MM/yyyy</string>
                 </property>
                 <property name="calendarPopup">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_11">
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>50</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background-color: rgb(46, 52, 54);</string>
        </property>
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout">
         <item row="0" column="0" alignment="Qt::AlignLeft">
          <widget class="QPushButton" name="cancel_button">
           <property name="font">
            <font>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">
background-color: rgba(198, 37, 37, 188);</string>
           </property>
           <property name="text">
            <string>Cancel</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1" alignment="Qt::AlignRight">
          <widget class="QPushButton" name="create_button">
           <property name="font">
            <font>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">
background-color: rgba(52, 214, 22, 133);</string>
           </property>
           <property name="text">
            <string>Create</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
