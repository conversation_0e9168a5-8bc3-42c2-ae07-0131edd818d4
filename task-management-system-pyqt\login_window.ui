<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>login_window</class>
 <widget class="QMainWindow" name="login_window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>605</width>
    <height>484</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>605</width>
    <height>484</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>605</width>
    <height>484</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Login</string>
  </property>
  <property name="windowOpacity">
   <double>0.920000000000000</double>
  </property>
  <property name="styleSheet">
   <string notr="true">QMainWindow#login_window{
	
	background-color: rgb(255, 255, 255);

}

</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QFrame" name="top_frame">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>********</width>
        <height>150</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QFrame#top_frame{
	background-color: rgb(46, 52, 54);
	border-radius:10px;

}

</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item alignment="Qt::AlignHCenter">
        <widget class="QLabel" name="welcome_label">
         <property name="font">
          <font>
           <family>Norasi</family>
           <pointsize>18</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">color: rgb(255, 255, 255);</string>
         </property>
         <property name="text">
          <string>Welcome To</string>
         </property>
        </widget>
       </item>
       <item alignment="Qt::AlignHCenter">
        <widget class="QLabel" name="title_label">
         <property name="font">
          <font>
           <family>Norasi</family>
           <pointsize>18</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">color: rgb(255, 255, 255);</string>
         </property>
         <property name="text">
          <string>Task Management System</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="low_frame">
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item alignment="Qt::AlignHCenter|Qt::AlignVCenter">
        <widget class="QFrame" name="mid_frame">
         <property name="maximumSize">
          <size>
           <width>********</width>
           <height>70</height>
          </size>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_6">
          <item alignment="Qt::AlignHCenter">
           <widget class="QLabel" name="login_label">
            <property name="maximumSize">
             <size>
              <width>170</width>
              <height>********</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Norasi</family>
              <pointsize>16</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>Log In</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="error_label">
            <property name="styleSheet">
             <string notr="true">color: rgb(239, 41, 41);</string>
            </property>
            <property name="text">
             <string>Invalid email or password</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="bottom_inner_frame">
         <property name="styleSheet">
          <string notr="true">QFrame#bottom_inner_frame{
	background-color: rgb(46, 52, 54);
	border-radius:10px;

}

</string>
         </property>
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <widget class="QFrame" name="left_frame">
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>********</height>
             </size>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_4">
             <item>
              <widget class="QLabel" name="email_label">
               <property name="font">
                <font>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>E-mail</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="password_label">
               <property name="font">
                <font>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>Password</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="right_frame">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_5">
             <item>
              <widget class="QLineEdit" name="email_input">
               <property name="inputMethodHints">
                <set>Qt::ImhNone</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="password_input">
               <property name="echoMode">
                <enum>QLineEdit::Password</enum>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item alignment="Qt::AlignBottom">
        <widget class="QFrame" name="buttom_frame">
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item alignment="Qt::AlignRight">
           <widget class="QPushButton" name="login_button">
            <property name="font">
             <font>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(46, 52, 54);
color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string>Login</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="account_frame">
      <property name="maximumSize">
       <size>
        <width>********</width>
        <height>60</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QFrame#account_frame{
	background-color: rgb(46, 52, 54);
	border-radius:10px;

}

</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_7">
       <item alignment="Qt::AlignRight">
        <widget class="QLabel" name="new_app_label">
         <property name="font">
          <font>
           <weight>75</weight>
           <italic>true</italic>
           <bold>true</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">color: rgb(255, 255, 255);</string>
         </property>
         <property name="text">
          <string>New To APP?</string>
         </property>
        </widget>
       </item>
       <item alignment="Qt::AlignRight">
        <widget class="QLabel" name="create_account_label">
         <property name="font">
          <font>
           <weight>75</weight>
           <italic>true</italic>
           <bold>true</bold>
           <underline>true</underline>
          </font>
         </property>
         <property name="cursor">
          <cursorShape>PointingHandCursor</cursorShape>
         </property>
         <property name="styleSheet">
          <string notr="true">color: rgb(52, 101, 164);</string>
         </property>
         <property name="text">
          <string>Create an Account.</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
