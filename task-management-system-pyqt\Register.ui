<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RegisterationWin</class>
 <widget class="QMainWindow" name="RegisterationWin">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>414</width>
    <height>698</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>414</width>
    <height>698</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>746</width>
    <height>806</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Registeration FORM</string>
  </property>
  <property name="windowOpacity">
   <double>0.970000000000000</double>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QFrame" name="top_frame">
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item alignment="Qt::AlignHCenter">
        <widget class="QLabel" name="welcome_lbl">
         <property name="font">
          <font>
           <pointsize>24</pointsize>
          </font>
         </property>
         <property name="text">
          <string>Welcome To</string>
         </property>
        </widget>
       </item>
       <item alignment="Qt::AlignHCenter">
        <widget class="QLabel" name="label_6">
         <property name="font">
          <font>
           <pointsize>19</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="text">
          <string>Task Management System</string>
         </property>
        </widget>
       </item>
       <item alignment="Qt::AlignHCenter">
        <widget class="QLabel" name="register_lbl">
         <property name="font">
          <font>
           <pointsize>24</pointsize>
          </font>
         </property>
         <property name="text">
          <string>Registeration Form</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="mid_frame">
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QFrame" name="frame">
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item alignment="Qt::AlignRight">
           <widget class="QLabel" name="fn_lbl">
            <property name="text">
             <string>First Name</string>
            </property>
           </widget>
          </item>
          <item alignment="Qt::AlignRight">
           <widget class="QLabel" name="lb_lbl">
            <property name="text">
             <string>Last Name</string>
            </property>
           </widget>
          </item>
          <item alignment="Qt::AlignRight">
           <widget class="QLabel" name="dob_lbl">
            <property name="text">
             <string>DOB</string>
            </property>
           </widget>
          </item>
          <item alignment="Qt::AlignRight">
           <widget class="QLabel" name="gender_lbl">
            <property name="text">
             <string>Gender</string>
            </property>
           </widget>
          </item>
          <item alignment="Qt::AlignRight">
           <widget class="QLabel" name="email_lbl">
            <property name="text">
             <string>E-mail</string>
            </property>
           </widget>
          </item>
          <item alignment="Qt::AlignRight">
           <widget class="QLabel" name="pass_lbl">
            <property name="text">
             <string>Password</string>
            </property>
           </widget>
          </item>
          <item alignment="Qt::AlignRight">
           <widget class="QLabel" name="confirm_pass_lbl">
            <property name="text">
             <string>Confirm Password</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_5">
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <item>
           <widget class="QLineEdit" name="fn_input"/>
          </item>
          <item>
           <widget class="QLineEdit" name="ln_input"/>
          </item>
          <item>
           <widget class="QDateEdit" name="dob_input">
            <property name="maximumDateTime">
             <datetime>
              <hour>23</hour>
              <minute>59</minute>
              <second>59</second>
              <year>3000</year>
              <month>12</month>
              <day>31</day>
             </datetime>
            </property>
            <property name="minimumDateTime">
             <datetime>
              <hour>0</hour>
              <minute>0</minute>
              <second>0</second>
              <year>1900</year>
              <month>1</month>
              <day>1</day>
             </datetime>
            </property>
            <property name="displayFormat">
             <string>dd/MM/yyyy</string>
            </property>
            <property name="calendarPopup">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="gender_input">
            <item>
             <property name="text">
              <string>Other</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>Male</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>Female</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="email_input"/>
          </item>
          <item>
           <widget class="QLineEdit" name="pass_input">
            <property name="echoMode">
             <enum>QLineEdit::Password</enum>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="confirm_pass_input">
            <property name="echoMode">
             <enum>QLineEdit::Password</enum>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="submit_frame">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>50</height>
       </size>
      </property>
      <property name="toolTip">
       <string/>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_5">
       <item alignment="Qt::AlignRight">
        <widget class="QPushButton" name="submit_button">
         <property name="text">
          <string>Submit</string>
         </property>
        </widget>
       </item>
       <item alignment="Qt::AlignRight">
        <widget class="QLabel" name="error_label">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="styleSheet">
          <string notr="true">color: rgb(204, 0, 0);
</string>
         </property>
         <property name="inputMethodHints">
          <set>Qt::ImhNone</set>
         </property>
         <property name="text">
          <string>TextLabel</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="bottom_frame">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_6">
       <item alignment="Qt::AlignRight|Qt::AlignBottom">
        <widget class="QLabel" name="account_lbl">
         <property name="text">
          <string>Already Have an Account? </string>
         </property>
        </widget>
       </item>
       <item alignment="Qt::AlignRight|Qt::AlignBottom">
        <widget class="QLabel" name="login_lbl">
         <property name="font">
          <font>
           <weight>75</weight>
           <italic>true</italic>
           <bold>true</bold>
           <underline>true</underline>
          </font>
         </property>
         <property name="cursor">
          <cursorShape>PointingHandCursor</cursorShape>
         </property>
         <property name="styleSheet">
          <string notr="true">color: rgb(52, 101, 164);</string>
         </property>
         <property name="text">
          <string>Login</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
