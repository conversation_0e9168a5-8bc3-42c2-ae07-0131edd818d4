# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'tms_main_window.ui'
#
# Created by: PyQt5 UI code generator 5.15.6
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_tms_window(object):
    def setupUi(self, tms_window):
        tms_window.setObjectName("tms_window")
        tms_window.resize(989, 881)
        tms_window.setWindowOpacity(0.97)
        tms_window.setStyleSheet("background-color: rgb(46, 52, 54);")
        self.centralwidget = QtWidgets.QWidget(tms_window)
        self.centralwidget.setObjectName("centralwidget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.centralwidget)
        self.verticalLayout.setContentsMargins(0, 0, -1, -1)
        self.verticalLayout.setObjectName("verticalLayout")
        self.hadder_frame = QtWidgets.QFrame(self.centralwidget)
        self.hadder_frame.setMaximumSize(QtCore.QSize(9999999, 80))
        self.hadder_frame.setStyleSheet("background-color: rgb(46, 52, 54);")
        self.hadder_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.hadder_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.hadder_frame.setObjectName("hadder_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.hadder_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, -1, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.left_header_frame = QtWidgets.QFrame(self.hadder_frame)
        self.left_header_frame.setMaximumSize(QtCore.QSize(200, 16777215))
        self.left_header_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.left_header_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.left_header_frame.setObjectName("left_header_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.left_header_frame)
        self.horizontalLayout_4.setContentsMargins(14, -1, -1, 0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.menu_button = QtWidgets.QPushButton(self.left_header_frame)
        self.menu_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.menu_button.setText("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/icons/menu.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.menu_button.setIcon(icon)
        self.menu_button.setIconSize(QtCore.QSize(32, 32))
        self.menu_button.setObjectName("menu_button")
        self.horizontalLayout_4.addWidget(self.menu_button)
        self.label_4 = QtWidgets.QLabel(self.left_header_frame)
        font = QtGui.QFont()
        font.setFamily("Nakula")
        font.setPointSize(16)
        font.setBold(True)
        font.setWeight(75)
        self.label_4.setFont(font)
        self.label_4.setCursor(QtGui.QCursor(QtCore.Qt.ArrowCursor))
        self.label_4.setStyleSheet("color: rgb(0, 0, 0);")
        self.label_4.setObjectName("label_4")
        self.horizontalLayout_4.addWidget(self.label_4)
        self.horizontalLayout_2.addWidget(self.left_header_frame, 0, QtCore.Qt.AlignLeft)
        self.name_frame = QtWidgets.QFrame(self.hadder_frame)
        self.name_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.name_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.name_frame.setObjectName("name_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.name_frame)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.live_lavel = QtWidgets.QLabel(self.name_frame)
        self.live_lavel.setMaximumSize(QtCore.QSize(16, 16))
        self.live_lavel.setToolTipDuration(3000)
        self.live_lavel.setText("")
        self.live_lavel.setPixmap(QtGui.QPixmap(":/icons/live.png"))
        self.live_lavel.setScaledContents(True)
        self.live_lavel.setObjectName("live_lavel")
        self.horizontalLayout_5.addWidget(self.live_lavel, 0, QtCore.Qt.AlignHCenter|QtCore.Qt.AlignVCenter)
        self.name_label = QtWidgets.QLabel(self.name_frame)
        font = QtGui.QFont()
        font.setFamily("Nakula")
        font.setPointSize(19)
        font.setBold(True)
        font.setWeight(75)
        self.name_label.setFont(font)
        self.name_label.setStyleSheet("color: rgb(255, 255, 255);")
        self.name_label.setObjectName("name_label")
        self.horizontalLayout_5.addWidget(self.name_label)
        self.horizontalLayout_2.addWidget(self.name_frame, 0, QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.right_hadder_frame = QtWidgets.QFrame(self.hadder_frame)
        self.right_hadder_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.right_hadder_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.right_hadder_frame.setObjectName("right_hadder_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.right_hadder_frame)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.logo_label = QtWidgets.QLabel(self.right_hadder_frame)
        self.logo_label.setMaximumSize(QtCore.QSize(50, 16777215))
        self.logo_label.setText("")
        self.logo_label.setPixmap(QtGui.QPixmap(":/icons/logo.png"))
        self.logo_label.setScaledContents(True)
        self.logo_label.setObjectName("logo_label")
        self.horizontalLayout_3.addWidget(self.logo_label)
        self.main_title_label = QtWidgets.QLabel(self.right_hadder_frame)
        font = QtGui.QFont()
        font.setFamily("Nakula")
        font.setPointSize(19)
        font.setBold(True)
        font.setWeight(75)
        self.main_title_label.setFont(font)
        self.main_title_label.setStyleSheet("color: rgb(255, 255, 255);")
        self.main_title_label.setObjectName("main_title_label")
        self.horizontalLayout_3.addWidget(self.main_title_label, 0, QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)
        self.horizontalLayout_2.addWidget(self.right_hadder_frame, 0, QtCore.Qt.AlignLeft)
        self.verticalLayout.addWidget(self.hadder_frame)
        self.body_frame = QtWidgets.QFrame(self.centralwidget)
        self.body_frame.setMinimumSize(QtCore.QSize(0, 500))
        self.body_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.body_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.body_frame.setObjectName("body_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.body_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.left_body_frame = QtWidgets.QFrame(self.body_frame)
        self.left_body_frame.setMinimumSize(QtCore.QSize(210, 2000))
        self.left_body_frame.setMaximumSize(QtCore.QSize(400, 16777215))
        self.left_body_frame.setStyleSheet("background-color: rgb(66, 66, 66);")
        self.left_body_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.left_body_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.left_body_frame.setObjectName("left_body_frame")
        self.inner_left_frame = QtWidgets.QFrame(self.left_body_frame)
        self.inner_left_frame.setGeometry(QtCore.QRect(9, 9, 203, 186))
        self.inner_left_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.inner_left_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.inner_left_frame.setObjectName("inner_left_frame")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.inner_left_frame)
        self.verticalLayout_4.setContentsMargins(0, 0, -1, 0)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.dashboard_frame = QtWidgets.QFrame(self.inner_left_frame)
        self.dashboard_frame.setStyleSheet("background-color: rgba(80, 80, 80, 193);")
        self.dashboard_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.dashboard_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.dashboard_frame.setObjectName("dashboard_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.dashboard_frame)
        self.horizontalLayout_6.setContentsMargins(0, -1, -1, -1)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.dashboard_button = QtWidgets.QPushButton(self.dashboard_frame)
        self.dashboard_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.dashboard_button.setToolTipDuration(2000)
        self.dashboard_button.setText("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/icons/dashboard.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.dashboard_button.setIcon(icon1)
        self.dashboard_button.setIconSize(QtCore.QSize(50, 50))
        self.dashboard_button.setAutoRepeat(False)
        self.dashboard_button.setAutoExclusive(False)
        self.dashboard_button.setFlat(True)
        self.dashboard_button.setObjectName("dashboard_button")
        self.horizontalLayout_6.addWidget(self.dashboard_button, 0, QtCore.Qt.AlignLeft)
        self.dashboard_label = QtWidgets.QLabel(self.dashboard_frame)
        font = QtGui.QFont()
        font.setPointSize(15)
        self.dashboard_label.setFont(font)
        self.dashboard_label.setStyleSheet("color: rgb(255, 255, 255);")
        self.dashboard_label.setObjectName("dashboard_label")
        self.horizontalLayout_6.addWidget(self.dashboard_label, 0, QtCore.Qt.AlignRight)
        self.verticalLayout_4.addWidget(self.dashboard_frame)
        self.profile_frame = QtWidgets.QFrame(self.inner_left_frame)
        self.profile_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.profile_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.profile_frame.setObjectName("profile_frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.profile_frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, -1, -1)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.profile_button = QtWidgets.QPushButton(self.profile_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.profile_button.sizePolicy().hasHeightForWidth())
        self.profile_button.setSizePolicy(sizePolicy)
        self.profile_button.setMinimumSize(QtCore.QSize(64, 64))
        self.profile_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.profile_button.setAcceptDrops(False)
        self.profile_button.setToolTipDuration(2000)
        self.profile_button.setAutoFillBackground(False)
        self.profile_button.setStyleSheet("QPushButton#profile_button\n"
"{\n"
"    background-color: rgb(46, 52, 54);\n"
"\n"
"}")
        self.profile_button.setText("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/icons/male-user.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.profile_button.setIcon(icon2)
        self.profile_button.setIconSize(QtCore.QSize(64, 64))
        self.profile_button.setAutoDefault(False)
        self.profile_button.setDefault(False)
        self.profile_button.setFlat(True)
        self.profile_button.setObjectName("profile_button")
        self.horizontalLayout_7.addWidget(self.profile_button, 0, QtCore.Qt.AlignLeft)
        self.profile_label = QtWidgets.QLabel(self.profile_frame)
        font = QtGui.QFont()
        font.setPointSize(15)
        font.setBold(True)
        font.setWeight(75)
        self.profile_label.setFont(font)
        self.profile_label.setStyleSheet("color: rgb(255, 255, 255);")
        self.profile_label.setObjectName("profile_label")
        self.horizontalLayout_7.addWidget(self.profile_label, 0, QtCore.Qt.AlignLeft)
        self.verticalLayout_4.addWidget(self.profile_frame)
        self.horizontalLayout.addWidget(self.left_body_frame, 0, QtCore.Qt.AlignHCenter|QtCore.Qt.AlignTop)
        self.right_body_frame = QtWidgets.QFrame(self.body_frame)
        self.right_body_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.right_body_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.right_body_frame.setLineWidth(1)
        self.right_body_frame.setObjectName("right_body_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.right_body_frame)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.task_manage_frame = QtWidgets.QFrame(self.right_body_frame)
        self.task_manage_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.task_manage_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.task_manage_frame.setObjectName("task_manage_frame")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.task_manage_frame)
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_3.setSpacing(0)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.frame = QtWidgets.QFrame(self.task_manage_frame)
        self.frame.setMinimumSize(QtCore.QSize(0, 70))
        self.frame.setStyleSheet("background-color: rgb(66, 66, 66);")
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.frame_2 = QtWidgets.QFrame(self.frame)
        self.frame_2.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.frame_2)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.task_top_frame_2 = QtWidgets.QFrame(self.frame_2)
        self.task_top_frame_2.setMinimumSize(QtCore.QSize(0, 55))
        self.task_top_frame_2.setMaximumSize(QtCore.QSize(70, 16777215))
        self.task_top_frame_2.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.task_top_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.task_top_frame_2.setObjectName("task_top_frame_2")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.task_top_frame_2)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.create_task_button = QtWidgets.QPushButton(self.task_top_frame_2)
        self.create_task_button.setMaximumSize(QtCore.QSize(39, 16777215))
        self.create_task_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.create_task_button.setText("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/icons/create_task.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.create_task_button.setIcon(icon3)
        self.create_task_button.setIconSize(QtCore.QSize(40, 40))
        self.create_task_button.setAutoDefault(True)
        self.create_task_button.setDefault(False)
        self.create_task_button.setFlat(True)
        self.create_task_button.setObjectName("create_task_button")
        self.horizontalLayout_11.addWidget(self.create_task_button, 0, QtCore.Qt.AlignHCenter)
        self.horizontalLayout_10.addWidget(self.task_top_frame_2, 0, QtCore.Qt.AlignLeft)
        self.task_top_frame = QtWidgets.QFrame(self.frame_2)
        self.task_top_frame.setMinimumSize(QtCore.QSize(0, 55))
        self.task_top_frame.setMaximumSize(QtCore.QSize(70, 16777215))
        self.task_top_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.task_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.task_top_frame.setObjectName("task_top_frame")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.task_top_frame)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.save_task_button = QtWidgets.QPushButton(self.task_top_frame)
        self.save_task_button.setMaximumSize(QtCore.QSize(39, 16777215))
        self.save_task_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.save_task_button.setText("")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/icons/save.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.save_task_button.setIcon(icon4)
        self.save_task_button.setIconSize(QtCore.QSize(40, 40))
        self.save_task_button.setAutoDefault(True)
        self.save_task_button.setFlat(True)
        self.save_task_button.setObjectName("save_task_button")
        self.horizontalLayout_12.addWidget(self.save_task_button, 0, QtCore.Qt.AlignHCenter)
        self.horizontalLayout_10.addWidget(self.task_top_frame)
        self.horizontalLayout_9.addWidget(self.frame_2, 0, QtCore.Qt.AlignLeft)
        self.verticalLayout_3.addWidget(self.frame, 0, QtCore.Qt.AlignTop)
        self.bottom_task_frame = QtWidgets.QFrame(self.task_manage_frame)
        self.bottom_task_frame.setMinimumSize(QtCore.QSize(0, 700))
        self.bottom_task_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.bottom_task_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.bottom_task_frame.setObjectName("bottom_task_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.bottom_task_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.tb = QtWidgets.QTableWidget(self.bottom_task_frame)
        self.tb.setMinimumSize(QtCore.QSize(602, 0))
        self.tb.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setFamily("Loma")
        font.setPointSize(11)
        font.setBold(False)
        font.setItalic(True)
        font.setWeight(9)
        self.tb.setFont(font)
        self.tb.setStyleSheet("font: 75 oblique 11pt \"Loma\";\n"
"color:rgb(255, 255, 255)")
        self.tb.setSizeAdjustPolicy(QtWidgets.QAbstractScrollArea.AdjustToContents)
        self.tb.setAutoScrollMargin(16)
        self.tb.setEditTriggers(QtWidgets.QAbstractItemView.AllEditTriggers)
        self.tb.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.tb.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.tb.setGridStyle(QtCore.Qt.CustomDashLine)
        self.tb.setWordWrap(True)
        self.tb.setObjectName("tb")
        self.tb.setColumnCount(6)
        self.tb.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        self.tb.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tb.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tb.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tb.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tb.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tb.setHorizontalHeaderItem(5, item)
        self.horizontalLayout_8.addWidget(self.tb)
        self.crud_frame = QtWidgets.QFrame(self.bottom_task_frame)
        self.crud_frame.setMinimumSize(QtCore.QSize(150, 0))
        self.crud_frame.setMaximumSize(QtCore.QSize(200, 16777215))
        self.crud_frame.setStyleSheet("background-color: rgb(66, 66, 66);")
        self.crud_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.crud_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.crud_frame.setObjectName("crud_frame")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.crud_frame)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.horizontalLayout_8.addWidget(self.crud_frame)
        self.verticalLayout_3.addWidget(self.bottom_task_frame)
        self.verticalLayout_2.addWidget(self.task_manage_frame)
        self.horizontalLayout.addWidget(self.right_body_frame)
        self.verticalLayout.addWidget(self.body_frame)
        tms_window.setCentralWidget(self.centralwidget)

        self.retranslateUi(tms_window)
        QtCore.QMetaObject.connectSlotsByName(tms_window)

    def retranslateUi(self, tms_window):
        _translate = QtCore.QCoreApplication.translate
        tms_window.setWindowTitle(_translate("tms_window", "Task Management System"))
        self.label_4.setText(_translate("tms_window", "Menu"))
        self.live_lavel.setToolTip(_translate("tms_window", "<html><head/><body><p><span style=\" font-weight:600; color:#eeeeec;\">Online</span></p></body></html>"))
        self.name_label.setText(_translate("tms_window", "Guest"))
        self.main_title_label.setText(_translate("tms_window", "Task Managenemt System"))
        self.dashboard_button.setToolTip(_translate("tms_window", "<html><head/><body><p><span style=\" font-weight:600; color:#ffffff;\">Dashboard</span></p></body></html>"))
        self.dashboard_label.setText(_translate("tms_window", "Dashboard"))
        self.profile_button.setToolTip(_translate("tms_window", "<html><head/><body><p><span style=\" font-weight:600; color:#ffffff;\">Profile</span></p></body></html>"))
        self.profile_label.setText(_translate("tms_window", "Profile"))
        self.create_task_button.setToolTip(_translate("tms_window", "<html><head/><body><p><span style=\" font-weight:600; color:#ffffff;\">Create New Task</span></p></body></html>"))
        self.save_task_button.setToolTip(_translate("tms_window", "<html><head/><body><p><span style=\" font-weight:600; color:#ffffff;\">Create New Task</span></p></body></html>"))
        item = self.tb.horizontalHeaderItem(0)
        item.setText(_translate("tms_window", "Task Id"))
        item = self.tb.horizontalHeaderItem(1)
        item.setText(_translate("tms_window", "Task Title"))
        item = self.tb.horizontalHeaderItem(2)
        item.setText(_translate("tms_window", "Description"))
        item = self.tb.horizontalHeaderItem(3)
        item.setText(_translate("tms_window", "Created at"))
        item = self.tb.horizontalHeaderItem(4)
        item.setText(_translate("tms_window", "Updated at"))
        item = self.tb.horizontalHeaderItem(5)
        item.setText(_translate("tms_window", "Status"))



import resources_rc
