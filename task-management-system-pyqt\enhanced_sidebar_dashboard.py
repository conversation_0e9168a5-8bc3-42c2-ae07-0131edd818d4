import sys
from PyQt5.QtWidgets import (QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QFrame, QStackedWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor

# Import des composants
from chart_widgets import MetricCard
from bureau_task_manager import BureauTaskManager

class EnhancedBureauButton(QPushButton):
    """Bouton de bureau avec compteur de tâches intégré"""
    
    def __init__(self, text, color, bureau_name, parent=None):
        super().__init__(parent)
        self.color = color
        self.bureau_name = bureau_name
        self.is_selected = False
        self.task_count = {"todo": 0, "progress": 0, "done": 0}
        
        self.setFixedHeight(70)  # Plus haut pour inclure les compteurs
        self.setFont(QFont("Segoe UI", 10, QFont.Bold))
        self.setup_ui(text)
        self.setup_style()
        
    def setup_ui(self, text):
        """Configure l'interface du bouton avec compteurs"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 8, 15, 8)
        layout.setSpacing(2)
        
        # Nom du bureau
        self.name_label = QLabel(text)
        self.name_label.setFont(QFont("Segoe UI", 10, QFont.Bold))
        self.name_label.setStyleSheet("color: white; background: transparent;")
        
        # Compteurs de tâches
        self.counters_label = QLabel("📋 0  ⚡ 0  ✅ 0")
        self.counters_label.setFont(QFont("Segoe UI", 8))
        self.counters_label.setStyleSheet("color: rgba(255,255,255,0.8); background: transparent;")
        
        layout.addWidget(self.name_label)
        layout.addWidget(self.counters_label)
        
    def setup_style(self):
        border_style = "border-left: 4px solid #00D4FF;" if self.is_selected else ""
        
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}, stop:1 {self.darken_color(self.color)});
                border: none;
                border-radius: 12px;
                color: white;
                text-align: left;
                margin: 5px;
                {border_style}
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.lighten_color(self.color)}, stop:1 {self.color});
            }}
            QPushButton:pressed {{
                background: {self.darken_color(self.color)};
            }}
        """)
    
    def update_task_counts(self, todo, progress, done):
        """Met à jour les compteurs de tâches"""
        self.task_count = {"todo": todo, "progress": progress, "done": done}
        self.counters_label.setText(f"📋 {todo}  ⚡ {progress}  ✅ {done}")
    
    def set_selected(self, selected):
        """Marque le bouton comme sélectionné"""
        self.is_selected = selected
        self.setup_style()
    
    def darken_color(self, color):
        color_map = {
            "#FF6B6B": "#E55555", "#4ECDC4": "#3BB5AE", "#45B7D1": "#3A9BC1",
            "#96CEB4": "#7FB89A", "#FECA57": "#E6B547", "#FF9FF3": "#E68FD3",
            "#54A0FF": "#4490EF", "#5F27CD": "#4F1FAD"
        }
        return color_map.get(color, color)
    
    def lighten_color(self, color):
        color_map = {
            "#FF6B6B": "#FF7B7B", "#4ECDC4": "#5EDDD4", "#45B7D1": "#55C7E1",
            "#96CEB4": "#A6DEC4", "#FECA57": "#FEDA67", "#FF9FF3": "#FFAFF3",
            "#54A0FF": "#64B0FF", "#5F27CD": "#6F37DD"
        }
        return color_map.get(color, color)

class TaskSummaryWidget(QWidget):
    """Widget de résumé des tâches pour un bureau"""
    
    def __init__(self, bureau_name, parent=None):
        super().__init__(parent)
        self.bureau_name = bureau_name
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête avec nom du bureau
        header_layout = QHBoxLayout()
        
        bureau_icon = self.get_bureau_icon()
        header = QLabel(f"{bureau_icon} Bureau {self.bureau_name}")
        header.setFont(QFont("Segoe UI", 18, QFont.Bold))
        header.setStyleSheet("color: white; margin-bottom: 15px;")
        
        # Bouton d'action rapide
        quick_add_btn = QPushButton("➕ Ajouter Tâche")
        quick_add_btn.setFixedHeight(35)
        quick_add_btn.setFixedWidth(130)
        quick_add_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4ECDC4, stop:1 #3BB5AE);
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #5EDDD4, stop:1 #4ECDC4);
            }
        """)
        
        header_layout.addWidget(header)
        header_layout.addStretch()
        header_layout.addWidget(quick_add_btn)
        
        # Cartes de statistiques spécifiques - Version compacte
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(10)  # Espacement réduit entre les cartes

        stats = self.get_bureau_stats()
        completed_card = MetricCard("Complétées", stats['completed'], stats['completed_trend'], "progress")
        in_progress_card = MetricCard("En Cours", stats['in_progress'], stats['progress_trend'], "progress")
        performance_card = MetricCard("Performance", stats['performance'], stats['perf_trend'], "line")

        # Réduire la taille des cartes
        for card in [completed_card, in_progress_card, performance_card]:
            card.setMaximumSize(220, 120)  # Taille maximale pour un meilleur affichage
            card.setMinimumSize(200, 100)  # Taille minimale

        stats_layout.addWidget(completed_card)
        stats_layout.addWidget(in_progress_card)
        stats_layout.addWidget(performance_card)
        stats_layout.addStretch()  # Ajouter un stretch pour centrer
        
        layout.addLayout(header_layout)
        layout.addLayout(stats_layout)
        layout.addStretch()
    
    def get_bureau_icon(self):
        """Retourne l'icône du bureau"""
        icons = {
            "1B": "📊", "2B": "👥", "3B": "💰", "4B": "🏗️", "5B": "📢",
            "B.GENIE": "🧠", "C.TRANS": "🚚", "SECRETARIAT": "📋"
        }
        return icons.get(self.bureau_name, "🏢")
    
    def get_bureau_stats(self):
        """Retourne des statistiques spécifiques au bureau"""
        bureau_stats = {
            "1B": {"completed": "18", "completed_trend": "↑ 15%", "in_progress": "5", "progress_trend": "↓ 2", "performance": "92%", "perf_trend": "↑ 8%"},
            "2B": {"completed": "24", "completed_trend": "↑ 12%", "in_progress": "8", "progress_trend": "↑ 3", "performance": "89%", "perf_trend": "↑ 5%"},
            "3B": {"completed": "16", "completed_trend": "↑ 20%", "in_progress": "6", "progress_trend": "→ 0", "performance": "85%", "perf_trend": "↑ 3%"},
            "4B": {"completed": "22", "completed_trend": "↑ 18%", "in_progress": "4", "progress_trend": "↓ 1", "performance": "94%", "perf_trend": "↑ 6%"},
            "5B": {"completed": "20", "completed_trend": "↑ 10%", "in_progress": "7", "progress_trend": "↑ 2", "performance": "88%", "perf_trend": "↑ 4%"},
            "B.GENIE": {"completed": "12", "completed_trend": "↑ 25%", "in_progress": "3", "progress_trend": "→ 0", "performance": "96%", "perf_trend": "↑ 12%"},
            "C.TRANS": {"completed": "28", "completed_trend": "↑ 14%", "in_progress": "9", "progress_trend": "↑ 4", "performance": "91%", "perf_trend": "↑ 7%"},
            "SECRETARIAT": {"completed": "35", "completed_trend": "↑ 22%", "in_progress": "12", "progress_trend": "↑ 5", "performance": "87%", "perf_trend": "↑ 9%"}
        }
        return bureau_stats.get(self.bureau_name, {"completed": "0", "completed_trend": "→ 0%", "in_progress": "0", "progress_trend": "→ 0", "performance": "0%", "perf_trend": "→ 0%"})
    


class EnhancedSidebarDashboard(QMainWindow):
    """Dashboard avec sidebar améliorée et affichage des tâches par bureau"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Dashboard Avancé - Gestion par Bureau")
        self.setGeometry(100, 100, 1400, 800)
        
        # Créer un gestionnaire de tâches global
        self.task_manager = BureauTaskManager()
        
        # Variables pour la navigation
        self.current_bureau_index = 0
        self.bureaux_names = ["1B", "2B", "3B", "4B", "5B", "B.GENIE", "C.TRANS", "SECRETARIAT"]
        
        self.setup_ui()
        self.apply_dark_theme()
        
        # Sélectionner le premier bureau par défaut
        self.switch_to_bureau(0)
        
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Sidebar améliorée
        self.create_enhanced_sidebar(main_layout)
        
        # Zone de contenu principal
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(10, 10, 10, 10)
        
        # Résumé du bureau (en haut)
        self.bureau_summary_stack = QStackedWidget()
        self.create_bureau_summaries()
        self.bureau_summary_stack.setFixedHeight(160)  # Hauteur fixe pour les cartes
        
        # Gestionnaire de tâches (en bas) - Version compacte
        task_frame = QFrame()
        task_frame.setStyleSheet("""
            QFrame {
                background-color: #2C2C54;
                border-radius: 15px;
                margin-top: 10px;
            }
        """)
        task_frame.setMaximumHeight(500)  # Limiter la hauteur du gestionnaire

        task_layout = QVBoxLayout(task_frame)
        task_layout.setContentsMargins(10, 10, 10, 10)

        task_title = QLabel("📋 Tâches du Bureau Sélectionné")
        task_title.setFont(QFont("Segoe UI", 12, QFont.Bold))  # Police plus petite
        task_title.setStyleSheet("color: white; margin-bottom: 5px;")

        # Configurer le gestionnaire de tâches pour être plus compact
        self.task_manager.setMaximumHeight(450)  # Limiter la hauteur

        task_layout.addWidget(task_title)
        task_layout.addWidget(self.task_manager)
        
        content_layout.addWidget(self.bureau_summary_stack)
        content_layout.addWidget(task_frame)
        
        main_layout.addWidget(content_widget, 1)
        
    def create_enhanced_sidebar(self, main_layout):
        """Crée la sidebar améliorée avec compteurs de tâches"""
        sidebar = QFrame()
        sidebar.setFixedWidth(280)  # Plus large pour les compteurs
        sidebar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1A1A2E, stop:1 #16213E);
                border-right: 1px solid #404040;
            }
        """)
        
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(15, 20, 15, 20)
        sidebar_layout.setSpacing(8)
        
        # Logo/Titre
        title = QLabel("🏢 TASK MANAGER")
        title.setFont(QFont("Segoe UI", 14, QFont.Bold))
        title.setStyleSheet("color: white; margin-bottom: 20px; text-align: center;")
        title.setAlignment(Qt.AlignCenter)
        
        sidebar_layout.addWidget(title)
        
        # Boutons de navigation améliorés
        self.nav_buttons = []
        bureaux = [
            ("1B", "#FF6B6B"), ("2B", "#4ECDC4"), ("3B", "#45B7D1"), ("4B", "#96CEB4"),
            ("5B", "#FECA57"), ("B.GENIE", "#FF9FF3"), ("C.TRANS", "#54A0FF"), ("SECRETARIAT", "#5F27CD")
        ]
        
        for i, (name, color) in enumerate(bureaux):
            btn = EnhancedBureauButton(f"🏢 {name}", color, name)
            btn.clicked.connect(lambda checked, idx=i: self.switch_to_bureau(idx))
            
            # Simuler des compteurs de tâches (à remplacer par de vraies données)
            import random
            todo = random.randint(2, 8)
            progress = random.randint(1, 5)
            done = random.randint(5, 15)
            btn.update_task_counts(todo, progress, done)
            
            self.nav_buttons.append(btn)
            sidebar_layout.addWidget(btn)
        
        sidebar_layout.addStretch()
        
        main_layout.addWidget(sidebar)
        
    def create_bureau_summaries(self):
        """Crée les résumés pour chaque bureau"""
        for bureau in self.bureaux_names:
            summary = TaskSummaryWidget(bureau)
            self.bureau_summary_stack.addWidget(summary)
    
    def switch_to_bureau(self, index):
        """Bascule vers le bureau sélectionné"""
        if 0 <= index < len(self.bureaux_names):
            self.current_bureau_index = index
            bureau_name = self.bureaux_names[index]
            
            # Mettre à jour l'affichage du résumé
            self.bureau_summary_stack.setCurrentIndex(index)
            
            # Basculer vers l'onglet du bureau dans le gestionnaire de tâches
            self.task_manager.switch_to_bureau(bureau_name)
            
            # Mettre à jour l'état des boutons
            for i, btn in enumerate(self.nav_buttons):
                btn.set_selected(i == index)
            
            # Mettre à jour le titre de la fenêtre
            self.setWindowTitle(f"Dashboard - Bureau {bureau_name} Sélectionné")
    
    def apply_dark_theme(self):
        """Applique le thème sombre moderne"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #0F0F23;
                color: white;
            }
            QWidget {
                background-color: transparent;
            }
        """)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setStyle("Fusion")
    
    # Palette sombre
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(15, 15, 35))
    palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
    app.setPalette(palette)
    
    dashboard = EnhancedSidebarDashboard()
    dashboard.show()
    
    sys.exit(app.exec_())
