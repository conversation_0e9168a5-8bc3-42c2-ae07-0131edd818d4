<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>tms_window</class>
 <widget class="QMainWindow" name="tms_window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>989</width>
    <height>881</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Task Management System</string>
  </property>
  <property name="windowOpacity">
   <double>0.970000000000000</double>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(46, 52, 54);</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QFrame" name="hadder_frame">
      <property name="maximumSize">
       <size>
        <width>9999999</width>
        <height>80</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgb(46, 52, 54);</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item alignment="Qt::AlignLeft">
        <widget class="QFrame" name="left_header_frame">
         <property name="maximumSize">
          <size>
           <width>200</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <property name="leftMargin">
           <number>14</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QPushButton" name="menu_button">
            <property name="cursor">
             <cursorShape>PointingHandCursor</cursorShape>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="icon">
             <iconset resource="resources/resources.qrc">
              <normaloff>:/icons/menu.png</normaloff>:/icons/menu.png</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>32</width>
              <height>32</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_4">
            <property name="font">
             <font>
              <family>Nakula</family>
              <pointsize>16</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="cursor">
             <cursorShape>ArrowCursor</cursorShape>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(0, 0, 0);</string>
            </property>
            <property name="text">
             <string>Menu</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item alignment="Qt::AlignLeft|Qt::AlignVCenter">
        <widget class="QFrame" name="name_frame">
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_5">
          <item alignment="Qt::AlignHCenter|Qt::AlignVCenter">
           <widget class="QLabel" name="live_lavel">
            <property name="maximumSize">
             <size>
              <width>16</width>
              <height>16</height>
             </size>
            </property>
            <property name="toolTip">
             <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600; color:#eeeeec;&quot;&gt;Online&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
            </property>
            <property name="toolTipDuration">
             <number>3000</number>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap resource="resources/resources.qrc">:/icons/live.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="name_label">
            <property name="font">
             <font>
              <family>Nakula</family>
              <pointsize>19</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string>Guest</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item alignment="Qt::AlignLeft">
        <widget class="QFrame" name="right_hadder_frame">
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <widget class="QLabel" name="logo_label">
            <property name="maximumSize">
             <size>
              <width>50</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap resource="resources/resources.qrc">:/icons/logo.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item alignment="Qt::AlignLeft|Qt::AlignTop">
           <widget class="QLabel" name="main_title_label">
            <property name="font">
             <font>
              <family>Nakula</family>
              <pointsize>19</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string>Task Managenemt System</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="body_frame">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>500</height>
       </size>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item alignment="Qt::AlignHCenter|Qt::AlignTop">
        <widget class="QFrame" name="left_body_frame">
         <property name="minimumSize">
          <size>
           <width>210</width>
           <height>2000</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>400</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(66, 66, 66);</string>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <widget class="QFrame" name="inner_left_frame">
          <property name="geometry">
           <rect>
            <x>9</x>
            <y>9</y>
            <width>203</width>
            <height>186</height>
           </rect>
          </property>
          <property name="frameShape">
           <enum>QFrame::NoFrame</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_4">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QFrame" name="dashboard_frame">
             <property name="styleSheet">
              <string notr="true">background-color: rgba(80, 80, 80, 193);</string>
             </property>
             <property name="frameShape">
              <enum>QFrame::NoFrame</enum>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Raised</enum>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_6">
              <property name="leftMargin">
               <number>0</number>
              </property>
              <item alignment="Qt::AlignLeft">
               <widget class="QPushButton" name="dashboard_button">
                <property name="cursor">
                 <cursorShape>PointingHandCursor</cursorShape>
                </property>
                <property name="toolTip">
                 <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600; color:#ffffff;&quot;&gt;Dashboard&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                </property>
                <property name="toolTipDuration">
                 <number>2000</number>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="icon">
                 <iconset resource="resources/resources.qrc">
                  <normaloff>:/icons/dashboard.png</normaloff>:/icons/dashboard.png</iconset>
                </property>
                <property name="iconSize">
                 <size>
                  <width>50</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="autoRepeat">
                 <bool>false</bool>
                </property>
                <property name="autoExclusive">
                 <bool>false</bool>
                </property>
                <property name="flat">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item alignment="Qt::AlignRight">
               <widget class="QLabel" name="dashboard_label">
                <property name="font">
                 <font>
                  <pointsize>15</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);</string>
                </property>
                <property name="text">
                 <string>Dashboard</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QFrame" name="profile_frame">
             <property name="frameShape">
              <enum>QFrame::NoFrame</enum>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Raised</enum>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_7">
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <item alignment="Qt::AlignLeft">
               <widget class="QPushButton" name="profile_button">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>64</width>
                  <height>64</height>
                 </size>
                </property>
                <property name="cursor">
                 <cursorShape>PointingHandCursor</cursorShape>
                </property>
                <property name="acceptDrops">
                 <bool>false</bool>
                </property>
                <property name="toolTip">
                 <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600; color:#ffffff;&quot;&gt;Profile&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                </property>
                <property name="toolTipDuration">
                 <number>2000</number>
                </property>
                <property name="autoFillBackground">
                 <bool>false</bool>
                </property>
                <property name="styleSheet">
                 <string notr="true">QPushButton#profile_button
{
	background-color: rgb(46, 52, 54);

}</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="icon">
                 <iconset resource="resources/resources.qrc">
                  <normaloff>:/icons/male-user.png</normaloff>:/icons/male-user.png</iconset>
                </property>
                <property name="iconSize">
                 <size>
                  <width>64</width>
                  <height>64</height>
                 </size>
                </property>
                <property name="autoDefault">
                 <bool>false</bool>
                </property>
                <property name="default">
                 <bool>false</bool>
                </property>
                <property name="flat">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item alignment="Qt::AlignLeft">
               <widget class="QLabel" name="profile_label">
                <property name="font">
                 <font>
                  <pointsize>15</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);</string>
                </property>
                <property name="text">
                 <string>Profile</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="right_body_frame">
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <property name="lineWidth">
          <number>1</number>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QFrame" name="task_manage_frame">
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_3">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item alignment="Qt::AlignTop">
              <widget class="QFrame" name="frame">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>70</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">background-color: rgb(66, 66, 66);</string>
               </property>
               <property name="frameShape">
                <enum>QFrame::StyledPanel</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <widget class="QFrame" name="frame_2">
                <property name="geometry">
                 <rect>
                  <x>10</x>
                  <y>10</y>
                  <width>201</width>
                  <height>75</height>
                 </rect>
                </property>
                <property name="frameShape">
                 <enum>QFrame::NoFrame</enum>
                </property>
                <property name="frameShadow">
                 <enum>QFrame::Raised</enum>
                </property>
                <layout class="QHBoxLayout" name="horizontalLayout_12">
                 <item>
                  <widget class="QFrame" name="task_top_frame_2">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>55</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>70</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="frameShape">
                    <enum>QFrame::NoFrame</enum>
                   </property>
                   <property name="frameShadow">
                    <enum>QFrame::Raised</enum>
                   </property>
                   <layout class="QHBoxLayout" name="horizontalLayout_9">
                    <item>
                     <widget class="QPushButton" name="create_task_button">
                      <property name="maximumSize">
                       <size>
                        <width>39</width>
                        <height>39</height>
                       </size>
                      </property>
                      <property name="cursor">
                       <cursorShape>PointingHandCursor</cursorShape>
                      </property>
                      <property name="toolTip">
                       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600; color:#ffffff;&quot;&gt;Create New Task&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                      </property>
                      <property name="text">
                       <string/>
                      </property>
                      <property name="icon">
                       <iconset resource="resources/resources.qrc">
                        <normaloff>:/icons/create_task.png</normaloff>:/icons/create_task.png</iconset>
                      </property>
                      <property name="iconSize">
                       <size>
                        <width>40</width>
                        <height>40</height>
                       </size>
                      </property>
                      <property name="autoDefault">
                       <bool>true</bool>
                      </property>
                      <property name="default">
                       <bool>false</bool>
                      </property>
                      <property name="flat">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </widget>
                 </item>
                 <item>
                  <widget class="QFrame" name="task_top_frame">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>55</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>70</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="frameShape">
                    <enum>QFrame::NoFrame</enum>
                   </property>
                   <property name="frameShadow">
                    <enum>QFrame::Raised</enum>
                   </property>
                   <layout class="QHBoxLayout" name="horizontalLayout_10">
                    <item>
                     <widget class="QPushButton" name="save_task_button">
                      <property name="maximumSize">
                       <size>
                        <width>39</width>
                        <height>39</height>
                       </size>
                      </property>
                      <property name="cursor">
                       <cursorShape>PointingHandCursor</cursorShape>
                      </property>
                      <property name="toolTip">
                       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600; color:#ffffff;&quot;&gt;Save Changes&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                      </property>
                      <property name="text">
                       <string/>
                      </property>
                      <property name="icon">
                       <iconset resource="resources/resources.qrc">
                        <normaloff>:/icons/save.png</normaloff>:/icons/save.png</iconset>
                      </property>
                      <property name="iconSize">
                       <size>
                        <width>40</width>
                        <height>40</height>
                       </size>
                      </property>
                      <property name="autoDefault">
                       <bool>true</bool>
                      </property>
                      <property name="flat">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </widget>
                 </item>
                 <item>
                  <widget class="QFrame" name="task_top_frame_3">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>55</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>70</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="frameShape">
                    <enum>QFrame::NoFrame</enum>
                   </property>
                   <property name="frameShadow">
                    <enum>QFrame::Raised</enum>
                   </property>
                   <layout class="QHBoxLayout" name="horizontalLayout_11">
                    <item>
                     <widget class="QPushButton" name="refresh_button">
                      <property name="maximumSize">
                       <size>
                        <width>39</width>
                        <height>39</height>
                       </size>
                      </property>
                      <property name="cursor">
                       <cursorShape>PointingHandCursor</cursorShape>
                      </property>
                      <property name="toolTip">
                       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600; color:#ffffff;&quot;&gt;Reload Data&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                      </property>
                      <property name="text">
                       <string/>
                      </property>
                      <property name="icon">
                       <iconset resource="resources/resources.qrc">
                        <normaloff>:/icons/refresh.png</normaloff>:/icons/refresh.png</iconset>
                      </property>
                      <property name="iconSize">
                       <size>
                        <width>40</width>
                        <height>40</height>
                       </size>
                      </property>
                      <property name="autoDefault">
                       <bool>true</bool>
                      </property>
                      <property name="flat">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </widget>
                 </item>
                </layout>
               </widget>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="bottom_task_frame">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>700</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::StyledPanel</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_8">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QTableWidget" name="tb">
                  <property name="minimumSize">
                   <size>
                    <width>602</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Loma</family>
                    <pointsize>11</pointsize>
                    <weight>9</weight>
                    <italic>true</italic>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">font: 75 oblique 11pt &quot;Loma&quot;;
color:rgb(255, 255, 255)</string>
                  </property>
                  <property name="sizeAdjustPolicy">
                   <enum>QAbstractScrollArea::AdjustToContents</enum>
                  </property>
                  <property name="autoScrollMargin">
                   <number>16</number>
                  </property>
                  <property name="editTriggers">
                   <set>QAbstractItemView::AllEditTriggers</set>
                  </property>
                  <property name="selectionMode">
                   <enum>QAbstractItemView::SingleSelection</enum>
                  </property>
                  <property name="selectionBehavior">
                   <enum>QAbstractItemView::SelectRows</enum>
                  </property>
                  <property name="gridStyle">
                   <enum>Qt::CustomDashLine</enum>
                  </property>
                  <property name="wordWrap">
                   <bool>true</bool>
                  </property>
                  <column>
                   <property name="text">
                    <string>Task Id</string>
                   </property>
                  </column>
                  <column>
                   <property name="text">
                    <string>Task Title</string>
                   </property>
                  </column>
                  <column>
                   <property name="text">
                    <string>Description</string>
                   </property>
                  </column>
                  <column>
                   <property name="text">
                    <string>Created at</string>
                   </property>
                  </column>
                  <column>
                   <property name="text">
                    <string>Updated at</string>
                   </property>
                  </column>
                  <column>
                   <property name="text">
                    <string>Status</string>
                   </property>
                  </column>
                 </widget>
                </item>
                <item>
                 <widget class="QFrame" name="crud_frame">
                  <property name="minimumSize">
                   <size>
                    <width>150</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>200</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">background-color: rgb(66, 66, 66);</string>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::StyledPanel</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_5"/>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="resources/resources.qrc"/>
 </resources>
 <connections/>
</ui>
