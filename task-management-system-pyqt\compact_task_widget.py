import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QScrollArea, QListWidget, 
                             QListWidgetItem, QDialog, QLineEdit, QComboBox, 
                             QFormLayout, QDialogButtonBox, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor

class QuickTaskDialog(QDialog):
    """Dialog simplifié pour créer une tâche rapidement"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Nouvelle Tâche")
        self.setFixedSize(350, 200)
        self.setup_ui()
        self.apply_style()
        
    def setup_ui(self):
        layout = QFormLayout(self)
        
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("<PERSON>itre de la tâche...")
        
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["Normale", "<PERSON>levée", "Urgente"])
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["À faire", "En cours", "Terminée"])
        
        layout.addRow("Titre:", self.title_edit)
        layout.addRow("Priorité:", self.priority_combo)
        layout.addRow("Statut:", self.status_combo)
        
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)
        
    def apply_style(self):
        self.setStyleSheet("""
            QDialog {
                background-color: #1A1A2E;
                color: white;
            }
            QLineEdit, QComboBox {
                background-color: #2C2C54;
                border: 1px solid #404040;
                border-radius: 5px;
                padding: 8px;
                color: white;
            }
            QPushButton {
                background-color: #4ECDC4;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5EDDD4;
            }
            QLabel {
                color: #B0B0B0;
                font-weight: bold;
            }
        """)
    
    def get_task_data(self):
        return {
            'title': self.title_edit.text(),
            'priority': self.priority_combo.currentText(),
            'status': self.status_combo.currentText()
        }

class TaskItem(QFrame):
    """Widget pour afficher une tâche individuelle"""
    
    def __init__(self, task_data, parent=None):
        super().__init__(parent)
        self.task_data = task_data
        self.setFixedHeight(60)
        self.setup_ui()
        self.apply_style()
        
    def setup_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # Indicateur de priorité
        priority_indicator = QFrame()
        priority_indicator.setFixedSize(4, 50)
        priority_colors = {
            "Normale": "#45B7D1",
            "Élevée": "#FECA57", 
            "Urgente": "#FF6B6B"
        }
        color = priority_colors.get(self.task_data.get('priority', 'Normale'), "#45B7D1")
        priority_indicator.setStyleSheet(f"background-color: {color}; border-radius: 2px;")
        
        # Contenu de la tâche
        content_layout = QVBoxLayout()
        content_layout.setSpacing(2)
        
        title_label = QLabel(self.task_data.get('title', 'Tâche sans titre'))
        title_label.setFont(QFont("Segoe UI", 10, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        
        status_label = QLabel(f"Statut: {self.task_data.get('status', 'À faire')}")
        status_label.setFont(QFont("Segoe UI", 8))
        status_label.setStyleSheet("color: #B0B0B0;")
        
        content_layout.addWidget(title_label)
        content_layout.addWidget(status_label)
        
        # Bouton d'action
        action_btn = QPushButton("✓")
        action_btn.setFixedSize(30, 30)
        action_btn.setStyleSheet("""
            QPushButton {
                background-color: #4ECDC4;
                border: none;
                border-radius: 15px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5EDDD4;
            }
        """)
        
        layout.addWidget(priority_indicator)
        layout.addLayout(content_layout)
        layout.addStretch()
        layout.addWidget(action_btn)
        
    def apply_style(self):
        self.setStyleSheet("""
            QFrame {
                background-color: #2C2C54;
                border: 1px solid #404040;
                border-radius: 8px;
                margin: 2px;
            }
            QFrame:hover {
                background-color: #3C3C64;
                border-color: #4ECDC4;
            }
        """)

class CompactTaskWidget(QWidget):
    """Widget compact pour la gestion des tâches dans le dashboard"""
    
    def __init__(self, bureau_name="Bureau", parent=None):
        super().__init__(parent)
        self.bureau_name = bureau_name
        self.tasks = []
        self.setup_ui()
        self.apply_style()
        self.add_sample_tasks()  # Ajouter quelques tâches d'exemple
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # En-tête avec bouton d'ajout
        header_layout = QHBoxLayout()
        
        add_btn = QPushButton("➕ Ajouter")
        add_btn.setFixedHeight(30)
        add_btn.setFixedWidth(100)
        add_btn.clicked.connect(self.add_task)
        
        view_all_btn = QPushButton("👁️ Tout voir")
        view_all_btn.setFixedHeight(30)
        view_all_btn.setFixedWidth(100)
        
        header_layout.addWidget(add_btn)
        header_layout.addWidget(view_all_btn)
        header_layout.addStretch()
        
        # Zone de défilement pour les tâches
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setMaximumHeight(200)
        
        # Widget conteneur pour les tâches
        self.tasks_container = QWidget()
        self.tasks_layout = QVBoxLayout(self.tasks_container)
        self.tasks_layout.setSpacing(5)
        self.tasks_layout.setContentsMargins(5, 5, 5, 5)
        
        scroll_area.setWidget(self.tasks_container)
        
        # Statistiques rapides
        stats_layout = QHBoxLayout()
        
        self.todo_count = QLabel("À faire: 0")
        self.todo_count.setStyleSheet("color: #FF6B6B; font-weight: bold;")
        
        self.progress_count = QLabel("En cours: 0")
        self.progress_count.setStyleSheet("color: #FECA57; font-weight: bold;")
        
        self.done_count = QLabel("Terminées: 0")
        self.done_count.setStyleSheet("color: #4ECDC4; font-weight: bold;")
        
        stats_layout.addWidget(self.todo_count)
        stats_layout.addWidget(self.progress_count)
        stats_layout.addWidget(self.done_count)
        
        layout.addLayout(header_layout)
        layout.addWidget(scroll_area)
        layout.addLayout(stats_layout)
        
    def apply_style(self):
        self.setStyleSheet("""
            QScrollArea {
                border: 1px solid #404040;
                border-radius: 8px;
                background-color: #1A1A2E;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4ECDC4, stop:1 #3BB5AE);
                border: none;
                border-radius: 6px;
                color: white;
                font-weight: bold;
                font-size: 9px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #5EDDD4, stop:1 #4ECDC4);
            }
            QScrollBar:vertical {
                background-color: #2C2C54;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #4ECDC4;
                border-radius: 4px;
                min-height: 20px;
            }
        """)
    
    def add_task(self):
        """Ajoute une nouvelle tâche"""
        dialog = QuickTaskDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            task_data = dialog.get_task_data()
            if task_data['title'].strip():  # Vérifier que le titre n'est pas vide
                self.tasks.append(task_data)
                self.refresh_tasks()
    
    def refresh_tasks(self):
        """Actualise l'affichage des tâches"""
        # Supprimer tous les widgets existants
        for i in reversed(range(self.tasks_layout.count())):
            child = self.tasks_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # Ajouter les tâches
        for task_data in self.tasks[-5:]:  # Afficher seulement les 5 dernières
            task_item = TaskItem(task_data)
            self.tasks_layout.addWidget(task_item)
        
        self.tasks_layout.addStretch()
        self.update_stats()
    
    def update_stats(self):
        """Met à jour les statistiques"""
        todo = len([t for t in self.tasks if t['status'] == 'À faire'])
        progress = len([t for t in self.tasks if t['status'] == 'En cours'])
        done = len([t for t in self.tasks if t['status'] == 'Terminée'])
        
        self.todo_count.setText(f"À faire: {todo}")
        self.progress_count.setText(f"En cours: {progress}")
        self.done_count.setText(f"Terminées: {done}")
    
    def add_sample_tasks(self):
        """Ajoute quelques tâches d'exemple"""
        sample_tasks = [
            {"title": "Réviser le rapport mensuel", "priority": "Élevée", "status": "À faire"},
            {"title": "Réunion équipe", "priority": "Normale", "status": "En cours"},
            {"title": "Mise à jour documentation", "priority": "Normale", "status": "Terminée"},
            {"title": "Traitement urgences", "priority": "Urgente", "status": "À faire"},
        ]
        
        self.tasks.extend(sample_tasks)
        self.refresh_tasks()

if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication, QMainWindow
    
    app = QApplication(sys.argv)
    
    window = QMainWindow()
    window.setWindowTitle("Widget Compact de Tâches")
    window.setGeometry(100, 100, 400, 400)
    
    window.setStyleSheet("""
        QMainWindow {
            background-color: #0F0F23;
        }
    """)
    
    task_widget = CompactTaskWidget("Bureau Test")
    window.setCentralWidget(task_widget)
    
    window.show()
    sys.exit(app.exec_())
